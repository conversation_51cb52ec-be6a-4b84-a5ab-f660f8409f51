library(ggthemes)
library(ggplot2) # package for plotting
library(png)
library(ncdf4)
library('reshape2')
library(we<PERSON><PERSON>on)
library(ggrepel)
library(dplyr)

mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)} #moving average fuction
standard_error <- function(x) sd(x) / sqrt(length(x)) # Create own function  (https://statisticsglobe.com/standard-error-in-r-example)

# Load data
PT_1CO2_som <- as.numeric(unlist(read.table("PT_1CO2_som.txt", header = FALSE)))
PT_2CO2_som <- as.numeric(unlist(read.table("PT_2CO2_som.txt", header = FALSE)))
PT_UNIF_som <- as.numeric(unlist(read.table("PT_UNIF_som.txt", header = FALSE)))
PT_SOLIN_som <- as.numeric(unlist(read.table("PT_SOLAR_som_29.txt", header = FALSE)))

TS_1CO2_som <- as.numeric(unlist(read.table("TS_1CO2_som.txt", header = FALSE)))
TS_2CO2_som <- as.numeric(unlist(read.table("TS_2CO2_som.txt", header = FALSE)))
TS_UNIF_som <- as.numeric(unlist(read.table("TS_UNIF_som.txt", header = FALSE)))
TS_SOLIN_som <- as.numeric(unlist(read.table("TS_SOLAR_som_29.txt", header = FALSE)))

PT_1CO2_sst <- as.numeric(unlist(read.table("PT_1CO2_sst.txt", header = FALSE)))
PT_2CO2_sst <- as.numeric(unlist(read.table("PT_2CO2_sst.txt", header = FALSE)))
PT_UNIF_sst <- as.numeric(unlist(read.table("PT_UNIF_sst.txt", header = FALSE)))
PT_SOLIN_sst <- as.numeric(unlist(read.table("PT_SOLAR_sst_29.txt", header = FALSE)))

TS_1CO2_sst <- as.numeric(unlist(read.table("TS_1CO2_sst.txt", header = FALSE)))
TS_2CO2_sst <- as.numeric(unlist(read.table("TS_2CO2_sst.txt", header = FALSE)))
TS_UNIF_sst <- as.numeric(unlist(read.table("TS_UNIF_sst.txt", header = FALSE)))
TS_SOLIN_sst <- as.numeric(unlist(read.table("TS_SOLAR_sst_29.txt", header = FALSE)))

# Calculate deltas with mixed reference points as requested
# 2CO2 relative to 1CO2
deltaP_2CO2_som=PT_2CO2_som-PT_1CO2_som
deltaP_2CO2_sst=PT_2CO2_sst-PT_1CO2_sst

# UNIF/SAI relative to 2CO2
deltaP_UNIF_som=PT_UNIF_som-PT_2CO2_som
deltaP_UNIF_sst=PT_UNIF_sst-PT_2CO2_sst

# SOLAR relative to 2CO2
# Make sure we're using the same number of years for both datasets
PT_2CO2_som_trimmed = PT_2CO2_som[1:length(PT_SOLIN_som)]
deltaP_SOLIN_som=PT_SOLIN_som-PT_2CO2_som_trimmed
deltaP_SOLIN_sst=PT_SOLIN_sst-PT_2CO2_sst

# Also calculate SAI and SOLAR relative to 1CO2
# UNIF/SAI relative to 1CO2
deltaP_UNIF_1CO2_som=PT_UNIF_som-PT_1CO2_som
deltaP_UNIF_1CO2_sst=PT_UNIF_sst-PT_1CO2_sst

# SOLAR relative to 1CO2
# Make sure we're using the same number of years for both datasets
PT_1CO2_som_trimmed = PT_1CO2_som[1:length(PT_SOLIN_som)]
deltaP_SOLIN_1CO2_som=PT_SOLIN_som-PT_1CO2_som_trimmed
deltaP_SOLIN_1CO2_sst=PT_SOLIN_sst-PT_1CO2_sst

# Temperature changes with the same reference points
# 2CO2 relative to 1CO2
deltaTS_2CO2_som=TS_2CO2_som-TS_1CO2_som
deltaTS_2CO2_sst=TS_2CO2_sst-TS_1CO2_sst

# UNIF/SAI relative to 2CO2
deltaTS_UNIF_som=TS_UNIF_som-TS_2CO2_som
deltaTS_UNIF_sst=TS_UNIF_sst-TS_2CO2_sst

# SOLAR relative to 2CO2
TS_2CO2_som_trimmed = TS_2CO2_som[1:length(TS_SOLIN_som)]
deltaTS_SOLIN_som=TS_SOLIN_som-TS_2CO2_som_trimmed
deltaTS_SOLIN_sst=TS_SOLIN_sst-TS_2CO2_sst

# Also calculate SAI and SOLAR relative to 1CO2
# UNIF/SAI relative to 1CO2
deltaTS_UNIF_1CO2_som=TS_UNIF_som-TS_1CO2_som
deltaTS_UNIF_1CO2_sst=TS_UNIF_sst-TS_1CO2_sst

# SOLAR relative to 1CO2
# Make sure we're using the same number of years for both datasets
TS_1CO2_som_trimmed = TS_1CO2_som[1:length(TS_SOLIN_som)]
deltaTS_SOLIN_1CO2_som=TS_SOLIN_som-TS_1CO2_som_trimmed
deltaTS_SOLIN_1CO2_sst=TS_SOLIN_sst-TS_1CO2_sst

# Make sure we're using the same length for PT_1CO2_som as deltaP_SOLIN_som
PT_1CO2_som_trimmed = PT_1CO2_som[1:length(PT_SOLIN_som)]

# Calculate precipitation changes as percentages
# Original scenarios
sai_som_precip = (mean(deltaP_UNIF_som)/mean(PT_1CO2_som))*100
sai_sst_precip = (mean(deltaP_UNIF_sst)/mean(PT_1CO2_sst))*100

solar_som_precip = (mean(deltaP_SOLIN_som)/mean(PT_1CO2_som_trimmed))*100
solar_sst_precip = (mean(deltaP_SOLIN_sst)/mean(PT_1CO2_sst))*100

# Create data frame with all scenarios
PT_mean = c(
  # Original scenarios
  (mean(deltaP_2CO2_som)/mean(PT_1CO2_som))*100,
  (mean(deltaP_2CO2_sst)/mean(PT_1CO2_sst))*100,
  sai_som_precip,
  sai_sst_precip,
  solar_som_precip,
  solar_sst_precip
)

# Temperature means - using absolute values
TS_mean = c(
  # Original scenarios
  abs(mean(deltaTS_2CO2_som)),
  0, # Set SST temperature to zero
  abs(mean(deltaTS_UNIF_som)),
  0, # Set SST temperature to zero
  abs(mean(deltaTS_SOLIN_som)),
  0  # Set SST temperature to zero
)

# Create data frame
df = data.frame(
  Experiment = c(
    "Global warming", "Global warming",
    "SAI", "SAI",
    "SOLAR", "SOLAR"
  ),
  Cntl = c("SOM", "SST", "SOM", "SST", "SOM", "SST"),
  PT_mean,
  PT_SE = c(
    # Original scenarios
    standard_error(deltaP_2CO2_som/PT_1CO2_som)*100,
    standard_error(deltaP_2CO2_sst/PT_1CO2_sst)*100,
    standard_error(deltaP_UNIF_som/PT_1CO2_som)*100,
    standard_error(deltaP_UNIF_sst/PT_1CO2_sst)*100,
    standard_error(deltaP_SOLIN_som/PT_1CO2_som_trimmed)*100,
    standard_error(deltaP_SOLIN_sst/PT_1CO2_sst)*100
  ),
  TS_mean,
  TS_SE = c(
    # Original scenarios
    standard_error(abs(deltaTS_2CO2_som)),
    0, # Set SST standard error to zero
    standard_error(abs(deltaTS_UNIF_som)),
    0, # Set SST standard error to zero
    standard_error(abs(deltaTS_SOLIN_som)),
    0  # Set SST standard error to zero
  )
)

print(df)

# Calculate apparent hydrological sensitivity (HS) for each experiment
# This is the ratio of precipitation change to temperature change
# For SOM points only (since SST temperature changes are set to zero)
HS_2CO2 = df$PT_mean[1]/df$TS_mean[1]
print("Apparent HS for Global warming (2CO2):")
print(HS_2CO2)

HS_SAI = df$PT_mean[3]/df$TS_mean[3]
print("Apparent HS for SAI:")
print(HS_SAI)

HS_SOLAR = df$PT_mean[5]/df$TS_mean[5]
print("Apparent HS for SOLAR:")
print(HS_SOLAR)

# Define colors and shapes
experiment_colors <- c(
  "Global warming" = "red",
  "SAI" = "blue",
  "SOLAR" = "green"
)
exp_shapes <- c(15,17)

# Create the plot
p <- ggplot(df, aes(x = TS_mean, y = PT_mean)) +
  theme_bw() +

  # Plot points for each experiment
  geom_point(data = df[c(1:2),], aes(shape = Cntl, color = Experiment), size = 6) +
  geom_point(data = df[c(3:4),], aes(shape = Cntl, color = Experiment), size = 6) +
  geom_point(data = df[c(5:6),], aes(shape = Cntl, color = Experiment), size = 6) +

  # Legend settings
  guides(shape = guide_legend(override.aes = list(shape = c(15,17)))) +
  guides(color = guide_legend(override.aes = list(shape = c(95,95,95), size = 8))) +

  # Add lines from origin to SOM points to show apparent HS
  # 2CO2-1CO2
  geom_segment(aes(x = 0, y = 0, xend = df$TS_mean[1], yend = df$PT_mean[1]),
               color = "red", linewidth = 1.7, linetype = "dashed") +
  # SAI-2CO2
  geom_segment(aes(x = 0, y = 0, xend = df$TS_mean[3], yend = df$PT_mean[3]),
               color = "blue", linewidth = 1.7, linetype = "dashed") +
  # SOLAR-2CO2
  geom_segment(aes(x = 0, y = 0, xend = df$TS_mean[5], yend = df$PT_mean[5]),
               color = "green", linewidth = 1.7, linetype = "dashed") +

  # Add lines for SAI-1CO2 and SOLAR-1CO2
  # SAI-1CO2
  geom_segment(aes(x = 0, y = 0, xend = abs(mean(deltaTS_UNIF_1CO2_som)),
                  yend = (mean(deltaP_UNIF_1CO2_som)/mean(PT_1CO2_som))*100),
               color = "blue", linewidth = 1.7, linetype = "dotted") +
  # SOLAR-1CO2
  geom_segment(aes(x = 0, y = 0, xend = abs(mean(deltaTS_SOLIN_1CO2_som)),
                  yend = (mean(deltaP_SOLIN_1CO2_som)/mean(PT_1CO2_som_trimmed))*100),
               color = "green", linewidth = 1.7, linetype = "dotted") +

  # Add reference lines
  geom_vline(xintercept = 0, linetype = "dashed", color = "black") +
  geom_hline(yintercept = 0, linetype = "dashed", color = "black") +

  # Add text labels for apparent HS values
  # 2CO2-1CO2
  annotate("text", x = df$TS_mean[1]/2, y = df$PT_mean[1]/2,
           label = paste0("HS = ", round(HS_2CO2, 2), "%/K"),
           color = "red", size = 5, angle = atan2(df$PT_mean[1], df$TS_mean[1])*180/pi) +
  # SAI-2CO2
  annotate("text", x = df$TS_mean[3]/2, y = df$PT_mean[3]/2,
           label = paste0("HS = ", round(HS_SAI, 2), "%/K"),
           color = "blue", size = 5, angle = atan2(df$PT_mean[3], df$TS_mean[3])*180/pi) +
  # SOLAR-2CO2
  annotate("text", x = df$TS_mean[5]/2, y = df$PT_mean[5]/2,
           label = paste0("HS = ", round(HS_SOLAR, 2), "%/K"),
           color = "green", size = 5, angle = atan2(df$PT_mean[5], df$TS_mean[5])*180/pi) +

  # SAI-1CO2
  annotate("text", x = abs(mean(deltaTS_UNIF_1CO2_som))/2,
           y = (mean(deltaP_UNIF_1CO2_som)/mean(PT_1CO2_som))*100/2,
           label = "SAI-1CO2",
           color = "blue", size = 5, angle = atan2((mean(deltaP_UNIF_1CO2_som)/mean(PT_1CO2_som))*100,
                                                 abs(mean(deltaTS_UNIF_1CO2_som)))*180/pi) +
  # SOLAR-1CO2
  annotate("text", x = abs(mean(deltaTS_SOLIN_1CO2_som))/2,
           y = (mean(deltaP_SOLIN_1CO2_som)/mean(PT_1CO2_som_trimmed))*100/2,
           label = "SOLAR-1CO2",
           color = "green", size = 5, angle = atan2((mean(deltaP_SOLIN_1CO2_som)/mean(PT_1CO2_som_trimmed))*100,
                                                  abs(mean(deltaTS_SOLIN_1CO2_som)))*180/pi) +

  # Labels and scales
  labs(x = "Temperature (K)", y = "Precipitation (%)", color = "Experiment", shape = "Control") +
  scale_color_manual(values = experiment_colors) +
  scale_shape_manual(values = exp_shapes) +
  scale_y_continuous(name = expression(~Delta~"Precipitation" ~ "(%)"), breaks = seq(-15, 15, 3), limits = c(-15.5, 15.5)) +
  scale_x_continuous(name = expression(~Delta~"Surface Temperature|" ~ "(K)"), breaks = seq(0, 6, 1), limits = c(0, 6.5)) +

  # Theme settings
  theme(axis.title.x = element_text(size = 22)) +
  theme(axis.title.y = element_text(size = 22)) +
  theme(plot.title = element_text(size = 22)) +
  theme(axis.text.x = element_blank()) +
  theme(axis.text.y = element_blank()) +
  ggtitle("Apparent Hydrological Sensitivity")

print(p)

# Save the plot
ggsave("HS_apparent.png", width = 20, height = 15, units = "cm", dpi = 300)
