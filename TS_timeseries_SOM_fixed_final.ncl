load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle" 
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle" 
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")

; Get latitude and longitude
lat = e_1CO2->lat
lon = e_1CO2->lon

; Calculate area weights for proper global averaging
rad = 4.0*atan(1.0)/180.0
clat = cos(lat*rad)

; Extract surface temperature data for all years
TS_SOM_1CO2 = e_1CO2->TS(39:99,:,:)
TS_SOM_2CO2 = e_2CO2->TS(39:99,:,:)
TS_SOM_UNIF = e_UNIF_22_5->TS(39:99,:,:)

; Define the SOLAR data as a 1D array
TS_SOM_SOLAR = (/289.98946, 290.00196, 289.96456, 289.89316, 289.85246, 289.95276, 289.93376, 289.94576, 290.10046, 289.94626, 289.91716, 289.88306, 290.02606, 290.00926, 289.89826, 289.94686, 289.96336, 289.93536, 289.87476, 289.97136, 290.01126, 289.98066, 289.89046, 289.99106, 289.90666, 289.89266, 289.92996, 290.04586, 290.02826, 290.13616, 290.14646, 290.03356, 289.99646, 290.05256, 290.07496, 290.01446, 290.03776, 290.03396, 290.05746, 290.10286, 290.09536, 290.00876, 290.05886, 290.00366, 290.07296, 289.98586, 290.10596, 290.06196, 290.07686, 290.06726, 290.14366, 290.05936, 290.01646, 290.11976, 290.18026, 290.10116, 290.01746, 289.99766, 289.94486, 289.85796/)

; Calculate global mean temperature for each year with proper area weighting
TS_SOM_1CO2_global = new(dimsizes(TS_SOM_1CO2(:,0,0)), float)
TS_SOM_2CO2_global = new(dimsizes(TS_SOM_2CO2(:,0,0)), float)
TS_SOM_UNIF_global = new(dimsizes(TS_SOM_UNIF(:,0,0)), float)

; Loop through each year to calculate global mean for 3D arrays
do i = 0, dimsizes(TS_SOM_1CO2(:,0,0))-1
  TS_SOM_1CO2_global(i) = wgt_areaave_Wrap(TS_SOM_1CO2(i,:,:), clat, 1.0, 0)
  TS_SOM_2CO2_global(i) = wgt_areaave_Wrap(TS_SOM_2CO2(i,:,:), clat, 1.0, 0)
  TS_SOM_UNIF_global(i) = wgt_areaave_Wrap(TS_SOM_UNIF(i,:,:), clat, 1.0, 0)
end do

; Print dimensions of arrays
print("Dimensions of TS_SOM_1CO2_global: " + dimsizes(TS_SOM_1CO2_global))
print("Dimensions of TS_SOM_SOLAR: " + dimsizes(TS_SOM_SOLAR))

; Check if the dimensions match
if (dimsizes(TS_SOM_1CO2_global) .ne. dimsizes(TS_SOM_SOLAR)) then
  print("Warning: Dimensions of TS_SOM_1CO2_global (" + dimsizes(TS_SOM_1CO2_global) + ") and TS_SOM_SOLAR (" + dimsizes(TS_SOM_SOLAR) + ") do not match.")
  
  ; Adjust the SOLAR data to match the dimensions of the other arrays
  if (dimsizes(TS_SOM_1CO2_global) .gt. dimsizes(TS_SOM_SOLAR)) then
    ; If SOLAR data is shorter, pad with the last value
    diff = dimsizes(TS_SOM_1CO2_global) - dimsizes(TS_SOM_SOLAR)
    TS_SOM_SOLAR_adjusted = new(dimsizes(TS_SOM_1CO2_global), float)
    TS_SOM_SOLAR_adjusted(0:dimsizes(TS_SOM_SOLAR)-1) = TS_SOM_SOLAR
    last_val = TS_SOM_SOLAR(dimsizes(TS_SOM_SOLAR)-1)
    do i = dimsizes(TS_SOM_SOLAR), dimsizes(TS_SOM_1CO2_global)-1
      TS_SOM_SOLAR_adjusted(i) = last_val
    end do
  else
    ; If SOLAR data is longer, truncate
    TS_SOM_SOLAR_adjusted = TS_SOM_SOLAR(0:dimsizes(TS_SOM_1CO2_global)-1)
  end if
else
  ; If dimensions match, use the original SOLAR data
  TS_SOM_SOLAR_adjusted = TS_SOM_SOLAR
end if

; Create time array (years)
nyears = dimsizes(TS_SOM_1CO2_global)
years = ispan(1, nyears, 1)

; Create a multi-dimensional array for temperature time series
tempTimeData = new((/4, nyears/), float)
tempTimeData(0,:) = TS_SOM_1CO2_global
tempTimeData(1,:) = TS_SOM_2CO2_global
tempTimeData(2,:) = TS_SOM_UNIF_global
tempTimeData(3,:) = TS_SOM_SOLAR_adjusted

; Set up plot resources
wks = gsn_open_wks("pdf","TS_timeseries_SOM_final")

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"black","red","blue","green"/)
res@xyLineThicknesses = (/2.5, 2.5, 2.5, 2.5/)
res@xyDashPatterns    = (/0, 0, 0, 0/)         ; all solid lines
res@gsnXYTopLabel     = False
res@tiMainString      = "Global Mean Surface Temperature (SOM)"
res@tiYAxisString     = "Temperature (K)"
res@tiXAxisString     = "Year"

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"1xCO~B~2", "2xCO~B~2", "SAI", "SOLAR"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.15                       ;-- define legend width
res@pmLegendHeightF        =  0.15   
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

; Create the plot
plot = gsn_csm_xy(wks, years, tempTimeData, res)

exit()
  
end
  
  
 
