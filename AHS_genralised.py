import matplotlib.pyplot as plt
import numpy as np

# Data for the graph - forcing types
forcing_types = ['1CO2', '2CO2', 'SAI', 'SOLAR']

# Using the data provided directly
print("Using provided data...")

# Temperature change values (in K) with respect to 1CO2
temp_change = {
    # SOM (coupled ocean) values
    'SOM': {
        '1CO2': 0.0,                # 1CO2 is the reference (zero change)
        '2CO2': 3.2804400,          # 2CO2 is warmer than 1CO2
        'SAI': 3.2804400 - 3.2606300,  # SAI relative to 1CO2
        'SOLAR': 3.2804400 - 5.5748400  # SOLAR relative to 1CO2
    },
    # SST (fixed SST) values
    'SST': {
        '1CO2': 0.0,                # 1CO2 is the reference (zero change)
        '2CO2': 0.2695800,          # 2CO2 relative to 1CO2
        'SAI': 0.2695800 - 0.1717367,  # SAI relative to 1CO2
        'SOLAR': 0.2695800 - 0.2718533  # SOLAR relative to 1CO2
    }
}

# Precipitation change values (in percentage) with respect to 1CO2
precip_change = {
    # SOM (coupled ocean) values
    'SOM': {
        '1CO2': 0.0,                 # 1CO2 is the reference (zero change)
        '2CO2': 5.9758618,           # 2CO2 relative to 1CO2
        'SAI': 5.9758618 - 8.4302264,   # SAI relative to 1CO2
        'SOLAR': 5.9758618 - 13.6198033  # SOLAR relative to 1CO2
    },
    # SST (fixed SST) values - fast response only
    'SST': {
        '1CO2': 0.0,                # 1CO2 is the reference (zero change)
        '2CO2': -2.4690197,          # 2CO2 relative to 1CO2 (negative because 1CO2 has more precipitation)
        'SAI': -2.4690197 + 0.6194838,  # SAI relative to 1CO2
        'SOLAR': -2.4690197 + 1.5980214  # SOLAR relative to 1CO2
    }
}

# Print the data read from files
print("\nTemperature changes (K):")
for model in ['SOM', 'SST']:
    print(f"  {model}:")
    for forcing in forcing_types:
        print(f"    {forcing}: {temp_change[model][forcing]:.4f}")

print("\nPrecipitation changes (%):")
for model in ['SOM', 'SST']:
    print(f"  {model}:")
    for forcing in forcing_types:
        print(f"    {forcing}: {precip_change[model][forcing]:.4f}")

# Calculate hydrological sensitivity (HS) as in the NCL script
# HS = (delta_PT_SOM - delta_PT_SST) / (delta_TS_SOM - delta_TS_SST)
HS = {}
for forcing in forcing_types:
    # Avoid division by zero
    denominator = temp_change['SOM'][forcing] - temp_change['SST'][forcing]
    if abs(denominator) < 1e-10:  # If denominator is very close to zero
        print(f"Warning: Temperature difference for {forcing} is too small. Using default HS value.")
        HS[forcing] = 0.0
    else:
        HS[forcing] = (precip_change['SOM'][forcing] - precip_change['SST'][forcing]) / denominator

# Calculate apparent hydrological sensitivity (AHS) as in the NCL script
# AHS = delta_PT_SOM / delta_TS_SOM
AHS = {}
for forcing in forcing_types:
    # Avoid division by zero
    if abs(temp_change['SOM'][forcing]) < 1e-10:  # If denominator is very close to zero
        print(f"Warning: SOM temperature change for {forcing} is too small. Using default AHS value.")
        AHS[forcing] = 0.0
    else:
        AHS[forcing] = precip_change['SOM'][forcing] / temp_change['SOM'][forcing]

print("Hydrological Sensitivity (HS):")
for forcing in forcing_types:
    print(f"{forcing}: {HS[forcing]:.2f} %/K")

print("\nApparent Hydrological Sensitivity (AHS):")
for forcing in forcing_types:
    print(f"{forcing}: {AHS[forcing]:.2f} %/K")

# Set up the figure and axis
fig, ax = plt.subplots(figsize=(10, 8))

# Colors for each forcing type
colors = {
    '1CO2': 'red',       # Red for 1CO2
    '2CO2': 'orange',    # Orange for 2CO2
    'SAI': 'blue',       # Blue for SAI
    'SOLAR': 'green'     # Green for Solar
}

# Plot precipitation vs temperature change
for forcing in forcing_types:
    # Plot SOM points (squares)
    ax.scatter(
        temp_change['SOM'][forcing],
        precip_change['SOM'][forcing],
        color=colors[forcing],
        marker='s',  # Square for SOM
        s=100,  # Size of marker
        label=f"{forcing} SOM"
    )

    # Plot SST points (triangles)
    ax.scatter(
        temp_change['SST'][forcing],
        precip_change['SST'][forcing],
        color=colors[forcing],
        marker='^',  # Triangle for SST
        s=100,  # Size of marker
        label=f"{forcing} SST"
    )

    # Draw lines connecting SOM and SST points
    ax.plot(
        [temp_change['SOM'][forcing], temp_change['SST'][forcing]],
        [precip_change['SOM'][forcing], precip_change['SST'][forcing]],
        color=colors[forcing],
        linestyle='-',
        linewidth=2
    )

# Add a horizontal line at y=0
ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)

# Add a vertical line at x=0
ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)

# Add labels and title
ax.set_xlabel('Δ Temperature (K)', fontsize=14)
ax.set_ylabel('Δ Precipitation (%)', fontsize=14)
ax.set_title('Hydrological Sensitivity (relative to 1CO2)', fontsize=16)

# Add grid
ax.grid(True, linestyle='--', alpha=0.7)

# Add a legend with custom handles
from matplotlib.lines import Line2D

# Create legend elements
legend_elements = []

# Control section (marker types)
legend_elements.append(Line2D([0], [0], marker='s', color='black', markerfacecolor='black',
                             markersize=8, linestyle='none', label='SOM'))
legend_elements.append(Line2D([0], [0], marker='^', color='black', markerfacecolor='black',
                             markersize=8, linestyle='none', label='SST'))

# Experiment section (colors)
legend_elements.append(Line2D([0], [0], marker='o', color='red', markerfacecolor='red',
                             markersize=8, linestyle='-', label='1CO2'))
legend_elements.append(Line2D([0], [0], marker='o', color='orange', markerfacecolor='orange',
                             markersize=8, linestyle='-', label='2CO2'))
legend_elements.append(Line2D([0], [0], marker='o', color='blue', markerfacecolor='blue',
                             markersize=8, linestyle='-', label='SAI'))
legend_elements.append(Line2D([0], [0], marker='o', color='green', markerfacecolor='green',
                             markersize=8, linestyle='-', label='SOLAR'))

# Create two-column legend
legend1 = ax.legend(handles=legend_elements[:2], loc='upper right', title='Control', fontsize=10, frameon=True)
legend2 = ax.legend(handles=legend_elements[2:], loc='lower right', title='Experiment', fontsize=10, frameon=True)

# Add the first legend back
ax.add_artist(legend1)

# Set axis limits to match the data
ax.set_xlim(-6.0, 0.5)
ax.set_ylim(-15, 4)

# Adjust layout and save the figure
plt.tight_layout()
plt.savefig('generalized_hydrological_sensitivity_plot.png', dpi=300)
plt.show()
