library(ggplot2)
library(dplyr)

# Create latitude grid
lat <- seq(-90, 90, by = 1)

# Function to create smooth curves based on typical climate response patterns
create_schematic_curves <- function(lat) {

  # Global Warming (2CO2-1CO2) patterns
  # Temperature: Arctic amplification (higher warming at poles, especially Arctic)
  gw_temp <- 2.5 + 1.5 * cos(lat * pi / 180)^2 +
             2.0 * exp(-((lat - 75)^2) / (2 * 15^2)) +  # Arctic amplification
             0.5 * exp(-((lat + 60)^2) / (2 * 20^2))    # Slight Antarctic amplification

  # Precipitation: Generally increases at high latitudes, decreases in subtropics
  gw_precip <- 0.3 * cos(3 * lat * pi / 180) +
               0.8 * exp(-((lat - 60)^2) / (2 * 25^2)) +  # High latitude increase
               0.6 * exp(-((lat + 60)^2) / (2 * 25^2)) -  # High latitude increase
               0.4 * exp(-((lat - 25)^2) / (2 * 15^2)) -  # Subtropical decrease
               0.4 * exp(-((lat + 25)^2) / (2 * 15^2))    # Subtropical decrease

  # SAI alone (relative to 2CO2) patterns
  # Temperature: Cooling, more uniform but with some latitudinal variation
  sai_temp <- -1.8 - 0.3 * cos(2 * lat * pi / 180) -
              0.5 * exp(-((lat - 45)^2) / (2 * 30^2)) -  # Mid-latitude cooling
              0.3 * exp(-((lat + 45)^2) / (2 * 30^2))    # Mid-latitude cooling

  # Precipitation: Complex pattern with regional variations
  sai_precip <- -0.2 * sin(2 * lat * pi / 180) -
                0.3 * exp(-((lat - 10)^2) / (2 * 20^2)) +  # Tropical changes
                0.2 * exp(-((lat - 50)^2) / (2 * 25^2)) +  # Mid-latitude changes
                0.2 * exp(-((lat + 50)^2) / (2 * 25^2)) -  # Mid-latitude changes
                0.1 * exp(-((lat + 10)^2) / (2 * 20^2))    # Tropical changes

  # Solar dimming alone (relative to 2CO2) patterns
  # Temperature: More uniform cooling than SAI
  solar_temp <- -1.2 - 0.2 * cos(lat * pi / 180) -
                0.3 * exp(-((lat)^2) / (2 * 40^2))        # Slight equatorial emphasis

  # Precipitation: Generally reduced, following solar heating patterns
  solar_precip <- -0.4 - 0.3 * cos(lat * pi / 180) +
                  0.1 * cos(3 * lat * pi / 180) -
                  0.2 * exp(-((lat)^2) / (2 * 30^2))       # Equatorial reduction

  return(data.frame(
    lat = lat,
    gw_temp = gw_temp,
    gw_precip = gw_precip,
    sai_temp = sai_temp,
    sai_precip = sai_precip,
    solar_temp = solar_temp,
    solar_precip = solar_precip
  ))
}

# Generate the schematic data
schematic_data <- create_schematic_curves(lat)

# Reshape data for plotting
temp_data <- data.frame(
  lat = rep(lat, 3),
  temperature = c(schematic_data$gw_temp, schematic_data$sai_temp, schematic_data$solar_temp),
  scenario = rep(c("Global Warming", "SAI", "Solar Dimming"), each = length(lat))
)

precip_data <- data.frame(
  lat = rep(lat, 3),
  precipitation = c(schematic_data$gw_precip, schematic_data$sai_precip, schematic_data$solar_precip),
  scenario = rep(c("Global Warming", "SAI", "Solar Dimming"), each = length(lat))
)

# Define colors and line styles
colors <- c("Global Warming" = "#d62728", "SAI" = "#1f77b4", "Solar Dimming" = "#2ca02c")
linetypes <- c("Global Warming" = "solid", "SAI" = "solid", "Solar Dimming" = "solid")

# Create temperature plot
temp_plot <- ggplot(temp_data, aes(x = lat, y = temperature, color = scenario, linetype = scenario)) +
  geom_line(linewidth = 2) +
  geom_hline(yintercept = 0, linetype = "dashed", color = "black", alpha = 0.5) +
  scale_color_manual(values = colors) +
  scale_linetype_manual(values = linetypes) +
  labs(
    title = "Schematic: Temperature Response vs Latitude",
    x = "Latitude (°)",
    y = "ΔTemperature (K)",
    color = "Scenario",
    linetype = "Scenario"
  ) +
  theme_bw() +
  theme(
    plot.title = element_text(size = 16, hjust = 0.5, face = "bold"),
    axis.title = element_text(size = 14),
    axis.text = element_text(size = 12),
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    legend.position = "bottom",
    panel.grid.minor = element_blank()
  ) +
  scale_x_continuous(breaks = seq(-90, 90, 30)) +
  scale_y_continuous(breaks = seq(-3, 6, 1))

# Create precipitation plot
precip_plot <- ggplot(precip_data, aes(x = lat, y = precipitation, color = scenario, linetype = scenario)) +
  geom_line(linewidth = 2) +
  geom_hline(yintercept = 0, linetype = "dashed", color = "black", alpha = 0.5) +
  scale_color_manual(values = colors) +
  scale_linetype_manual(values = linetypes) +
  labs(
    title = "Schematic: Precipitation Response vs Latitude",
    x = "Latitude (°)",
    y = "ΔPrecipitation (mm/day)",
    color = "Scenario",
    linetype = "Scenario"
  ) +
  theme_bw() +
  theme(
    plot.title = element_text(size = 16, hjust = 0.5, face = "bold"),
    axis.title = element_text(size = 14),
    axis.text = element_text(size = 12),
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    legend.position = "bottom",
    panel.grid.minor = element_blank()
  ) +
  scale_x_continuous(breaks = seq(-90, 90, 30)) +
  scale_y_continuous(breaks = seq(-1, 2, 0.5))

# Save individual plots
ggsave("schematic_temperature_vs_latitude.png", temp_plot, width = 12, height = 8, dpi = 300)
ggsave("schematic_precipitation_vs_latitude.png", precip_plot, width = 12, height = 8, dpi = 300)

# Create a summary table of key characteristics
summary_table <- data.frame(
  Scenario = c("Global Warming", "SAI", "Solar Dimming"),
  Temperature_Pattern = c(
    "Arctic amplification, general warming",
    "Uniform cooling, reduced Arctic amplification",
    "Uniform cooling, slight equatorial emphasis"
  ),
  Precipitation_Pattern = c(
    "High-lat increase, subtropical decrease",
    "Complex regional variations",
    "General reduction, follows solar pattern"
  ),
  Max_Temp_Change = c(
    round(max(schematic_data$gw_temp), 1),
    round(max(schematic_data$sai_temp), 1),
    round(max(schematic_data$solar_temp), 1)
  ),
  Min_Temp_Change = c(
    round(min(schematic_data$gw_temp), 1),
    round(min(schematic_data$sai_temp), 1),
    round(min(schematic_data$solar_temp), 1)
  )
)

print("Summary of Schematic Patterns:")
print(summary_table)

# Print key features
cat("\nKey Features of Each Scenario:\n")
cat("=====================================\n")
cat("Global Warming (2CO2-1CO2):\n")
cat("- Strong Arctic amplification (up to", round(max(schematic_data$gw_temp), 1), "K warming)\n")
cat("- Precipitation increases at high latitudes\n")
cat("- Precipitation decreases in subtropics\n\n")

cat("SAI (relative to 2CO2):\n")
cat("- Uniform cooling (", round(min(schematic_data$sai_temp), 1), "to", round(max(schematic_data$sai_temp), 1), "K)\n")
cat("- Complex precipitation patterns\n")
cat("- Reduced hydrological cycle intensity\n\n")

cat("Solar Dimming (relative to 2CO2):\n")
cat("- More uniform cooling than SAI\n")
cat("- General precipitation reduction\n")
cat("- Follows solar heating patterns\n\n")

cat("Plots saved:\n")
cat("- schematic_temperature_vs_latitude.png\n")
cat("- schematic_precipitation_vs_latitude.png\n")

# Create a simple conceptual diagram showing the three scenarios
conceptual_data <- data.frame(
  scenario = c("Global Warming", "SAI", "Solar Dimming"),
  description = c("CO₂ ↑ → Warming", "Aerosols ↑ → Cooling", "Solar ↓ → Cooling"),
  color = c("#d62728", "#1f77b4", "#2ca02c")
)

conceptual_plot <- ggplot(conceptual_data, aes(x = 1:3, y = 1, fill = color)) +
  geom_tile(width = 0.8, height = 0.3, color = "white", linewidth = 2) +
  geom_text(aes(label = scenario), size = 5, fontface = "bold", color = "white") +
  geom_text(aes(label = description, y = 0.6), size = 4, color = "black") +
  scale_fill_identity() +
  theme_void() +
  theme(
    plot.title = element_text(size = 16, hjust = 0.5, face = "bold"),
    plot.margin = margin(20, 20, 20, 20)
  ) +
  labs(title = "Three Climate Intervention Scenarios") +
  coord_cartesian(xlim = c(0.5, 3.5), ylim = c(0.4, 1.4))

ggsave("conceptual_scenarios.png", conceptual_plot, width = 10, height = 4, dpi = 300)

cat("- conceptual_scenarios.png\n")
