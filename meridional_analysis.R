library(ggplot2)
library(dplyr)
library(ncdf4)
library(reshape2)

# Function to calculate area-weighted zonal means
calc_zonal_mean <- function(data, lat) {
  # Calculate cosine weights for area weighting
  weights <- cos(lat * pi / 180)
  
  # Calculate zonal mean for each latitude
  zonal_means <- apply(data, 2, function(x) {
    weighted.mean(x, weights, na.rm = TRUE)
  })
  
  return(zonal_means)
}

# Function to read NetCDF data and calculate zonal means
read_and_process_data <- function(file_path, var_name, time_range, lat_dim = 2, lon_dim = 3) {
  nc_file <- nc_open(file_path)
  
  # Read data
  data <- ncvar_get(nc_file, var_name)
  lat <- ncvar_get(nc_file, "lat")
  lon <- ncvar_get(nc_file, "lon")
  
  nc_close(nc_file)
  
  # Select time range
  if (length(dim(data)) == 3) {
    data_subset <- data[time_range, , ]
  } else {
    data_subset <- data
  }
  
  # Calculate time mean first
  if (length(dim(data_subset)) == 3) {
    time_mean <- apply(data_subset, c(2, 3), mean, na.rm = TRUE)
  } else {
    time_mean <- data_subset
  }
  
  # Calculate zonal mean with area weighting
  zonal_mean <- apply(time_mean, 1, function(x) {
    weights <- cos(lat * pi / 180)
    weighted.mean(x, weights, na.rm = TRUE)
  })
  
  return(list(zonal_mean = zonal_mean, lat = lat))
}

# Define file paths (you may need to adjust these paths)
base_path <- "/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"

# File paths for SOM runs
som_files <- list(
  "1CO2" = file.path(base_path, "Yearly_E_CO2_01_100new1.nc"),
  "2CO2" = file.path(base_path, "Yearly_E_2CO2_01_100new1.nc"),
  "UNIF" = file.path(base_path, "Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc"),
  "SOLAR" = "/home/<USER>/Documents/Data_solin/E2000_solin/Yearly_E2000_solin_01_100_1.nc"
)

# File paths for SST runs
sst_files <- list(
  "1CO2" = file.path(base_path, "Yearly_F_CO2_01_60_new1.nc"),
  "2CO2" = file.path(base_path, "Yearly_F_2CO2_01_60_new1.nc"),
  "UNIF" = file.path(base_path, "Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc"),
  "SOLAR" = "/home/<USER>/Documents/Data_solin/F2000_solin/Yearly_F2000_solin_01_60_1.nc"
)

# Time ranges
som_time_range <- 41:100  # Years 40-99 (0-indexed becomes 41:100)
sst_time_range <- 31:60   # Years 30-59 (0-indexed becomes 31:60)

# Process precipitation data (PRECC + PRECL)
process_precip_data <- function(file_path, time_range) {
  nc_file <- nc_open(file_path)
  
  precc <- ncvar_get(nc_file, "PRECC")
  precl <- ncvar_get(nc_file, "PRECL")
  lat <- ncvar_get(nc_file, "lat")
  
  nc_close(nc_file)
  
  # Combine convective and large-scale precipitation and convert to mm/day
  total_precip <- (precc + precl) * 86400 * 1000
  
  # Select time range
  precip_subset <- total_precip[time_range, , ]
  
  # Calculate time mean
  time_mean <- apply(precip_subset, c(2, 3), mean, na.rm = TRUE)
  
  # Calculate zonal mean with area weighting
  zonal_mean <- apply(time_mean, 1, function(x) {
    weights <- cos(lat * pi / 180)
    weighted.mean(x, weights, na.rm = TRUE)
  })
  
  return(list(zonal_mean = zonal_mean, lat = lat))
}

# Process temperature data
process_temp_data <- function(file_path, time_range) {
  result <- read_and_process_data(file_path, "TS", time_range)
  return(result)
}

# Check if files exist before processing
check_files <- function(file_list) {
  for (name in names(file_list)) {
    if (!file.exists(file_list[[name]])) {
      cat("Warning: File not found:", file_list[[name]], "\n")
      return(FALSE)
    }
  }
  return(TRUE)
}

# Process SOM data
if (check_files(som_files)) {
  cat("Processing SOM precipitation data...\n")
  som_precip <- list()
  for (name in names(som_files)) {
    som_precip[[name]] <- process_precip_data(som_files[[name]], som_time_range)
  }
  
  cat("Processing SOM temperature data...\n")
  som_temp <- list()
  for (name in names(som_files)) {
    som_temp[[name]] <- process_temp_data(som_files[[name]], som_time_range)
  }
} else {
  cat("Some SOM files are missing. Skipping SOM analysis.\n")
}

# Process SST data
if (check_files(sst_files)) {
  cat("Processing SST precipitation data...\n")
  sst_precip <- list()
  for (name in names(sst_files)) {
    sst_precip[[name]] <- process_precip_data(sst_files[[name]], sst_time_range)
  }
  
  cat("Processing SST temperature data...\n")
  sst_temp <- list()
  for (name in names(sst_files)) {
    sst_temp[[name]] <- process_temp_data(sst_files[[name]], sst_time_range)
  }
} else {
  cat("Some SST files are missing. Skipping SST analysis.\n")
}

# Calculate differences
calc_differences <- function(data_list) {
  lat <- data_list[["1CO2"]]$lat
  
  # 2CO2 - 1CO2
  diff_2CO2 <- data_list[["2CO2"]]$zonal_mean - data_list[["1CO2"]]$zonal_mean
  
  # UNIF - 2CO2
  diff_UNIF <- data_list[["UNIF"]]$zonal_mean - data_list[["2CO2"]]$zonal_mean
  
  # SOLAR - 2CO2
  diff_SOLAR <- data_list[["SOLAR"]]$zonal_mean - data_list[["2CO2"]]$zonal_mean
  
  # UNIF - 1CO2
  diff_UNIF_1CO2 <- data_list[["UNIF"]]$zonal_mean - data_list[["1CO2"]]$zonal_mean
  
  # SOLAR - 1CO2
  diff_SOLAR_1CO2 <- data_list[["SOLAR"]]$zonal_mean - data_list[["1CO2"]]$zonal_mean
  
  return(list(
    lat = lat,
    "2CO2-1CO2" = diff_2CO2,
    "SAI-2CO2" = diff_UNIF,
    "SOLAR-2CO2" = diff_SOLAR,
    "SAI-1CO2" = diff_UNIF_1CO2,
    "SOLAR-1CO2" = diff_SOLAR_1CO2
  ))
}

# Calculate precipitation differences
if (exists("som_precip") && exists("sst_precip")) {
  som_precip_diff <- calc_differences(som_precip)
  sst_precip_diff <- calc_differences(sst_precip)
  
  som_temp_diff <- calc_differences(som_temp)
  sst_temp_diff <- calc_differences(sst_temp)
  
  cat("Data processing completed successfully!\n")
  cat("Available scenarios:", names(som_precip_diff)[-1], "\n")
} else {
  cat("Data processing failed. Please check file paths.\n")
}

# Create plotting function
create_meridional_plot <- function(data_list, title, ylabel, filename) {
  # Prepare data for plotting
  lat <- data_list$lat
  scenarios <- names(data_list)[-1]  # Exclude 'lat'
  
  plot_data <- data.frame(
    lat = rep(lat, length(scenarios)),
    value = unlist(data_list[scenarios]),
    scenario = rep(scenarios, each = length(lat))
  )
  
  # Define colors and line types
  colors <- c("2CO2-1CO2" = "red", "SAI-2CO2" = "blue", "SOLAR-2CO2" = "green",
              "SAI-1CO2" = "blue", "SOLAR-1CO2" = "green")
  linetypes <- c("2CO2-1CO2" = "solid", "SAI-2CO2" = "solid", "SOLAR-2CO2" = "solid",
                 "SAI-1CO2" = "dashed", "SOLAR-1CO2" = "dashed")
  
  p <- ggplot(plot_data, aes(x = lat, y = value, color = scenario, linetype = scenario)) +
    geom_line(size = 1.2) +
    geom_hline(yintercept = 0, linetype = "dashed", color = "black", alpha = 0.5) +
    scale_color_manual(values = colors) +
    scale_linetype_manual(values = linetypes) +
    labs(
      title = title,
      x = "Latitude (°)",
      y = ylabel,
      color = "Scenario",
      linetype = "Scenario"
    ) +
    theme_bw() +
    theme(
      plot.title = element_text(size = 14, hjust = 0.5),
      axis.title = element_text(size = 12),
      legend.title = element_text(size = 11),
      legend.text = element_text(size = 10)
    ) +
    scale_x_continuous(breaks = seq(-90, 90, 30))
  
  ggsave(filename, plot = p, width = 10, height = 6, dpi = 300)
  return(p)
}

# Generate plots if data is available
if (exists("som_precip_diff") && exists("sst_precip_diff")) {
  # Precipitation plots
  p1 <- create_meridional_plot(som_precip_diff, 
                              "Precipitation Change vs Latitude (SOM runs)", 
                              "ΔPrecipitation (mm/day)",
                              "precipitation_vs_lat_SOM.png")
  
  p2 <- create_meridional_plot(sst_precip_diff, 
                              "Precipitation Change vs Latitude (SST runs)", 
                              "ΔPrecipitation (mm/day)",
                              "precipitation_vs_lat_SST.png")
  
  # Temperature plots
  p3 <- create_meridional_plot(som_temp_diff, 
                              "Temperature Change vs Latitude (SOM runs)", 
                              "ΔTemperature (K)",
                              "temperature_vs_lat_SOM.png")
  
  p4 <- create_meridional_plot(sst_temp_diff, 
                              "Temperature Change vs Latitude (SST runs)", 
                              "ΔTemperature (K)",
                              "temperature_vs_lat_SST.png")
  
  cat("Plots saved successfully!\n")
  cat("Generated files:\n")
  cat("- precipitation_vs_lat_SOM.png\n")
  cat("- precipitation_vs_lat_SST.png\n")
  cat("- temperature_vs_lat_SOM.png\n")
  cat("- temperature_vs_lat_SST.png\n")
} else {
  cat("Cannot generate plots. Data processing incomplete.\n")
}
