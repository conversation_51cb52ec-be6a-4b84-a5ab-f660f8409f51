library(ggthemes)
# setwd("/Volumes/CAOS_2/Analysis/292.7/Figures/Sensitivity_Figures/ISMI_ERF_TG/CleanFigure")
library(ggplot2) # package for plotting
library(png)
library(ncdf4)
library('reshape2')
library(we<PERSON><PERSON>on)
library(ggrepel)
# library(ggpubr)
library(dplyr)
# library("r2symbols")
mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)} #moving average fuction
standard_error <- function(x) sd(x) / sqrt(length(x)) # Create own function  (https://statisticsglobe.com/standard-error-in-r-example)
PT_1CO2_som <- as.numeric(unlist(read.table("PT_1CO2_som.txt", header = FALSE)))
PT_2CO2_som <- as.numeric(unlist(read.table("PT_2CO2_som.txt", header = FALSE)))
PT_UNIF_som <- as.numeric(unlist(read.table("PT_UNIF_som.txt", header = FALSE)))
PT_SOLIN_som <- as.numeric(unlist(read.table("PT_SOLAR_som.txt", header = FALSE)))

TS_1CO2_som <- as.numeric(unlist(read.table("TS_1CO2_som.txt", header = FALSE)))
TS_2CO2_som <- as.numeric(unlist(read.table("TS_2CO2_som.txt", header = FALSE)))
TS_UNIF_som <- as.numeric(unlist(read.table("TS_UNIF_som.txt", header = FALSE)))
TS_SOLIN_som <- as.numeric(unlist(read.table("TS_SOLAR_som.txt", header = FALSE)))

PT_1CO2_sst <- as.numeric(unlist(read.table("PT_1CO2_sst.txt", header = FALSE)))
PT_2CO2_sst <- as.numeric(unlist(read.table("PT_2CO2_sst.txt", header = FALSE)))
PT_UNIF_sst <- as.numeric(unlist(read.table("PT_UNIF_sst.txt", header = FALSE)))
PT_SOLIN_sst <- as.numeric(unlist(read.table("PT_SOLAR_sst.txt", header = FALSE)))

TS_1CO2_sst <- as.numeric(unlist(read.table("TS_1CO2_sst.txt", header = FALSE)))
TS_2CO2_sst <- as.numeric(unlist(read.table("TS_2CO2_sst.txt", header = FALSE)))
TS_UNIF_sst <- as.numeric(unlist(read.table("TS_UNIF_sst.txt", header = FALSE)))
TS_SOLIN_sst <- as.numeric(unlist(read.table("TS_SOLAR_sst.txt", header = FALSE)))

deltaP_1CO2_som=PT_1CO2_som-PT_2CO2_som
deltaP_UNIF_som=PT_UNIF_som-PT_2CO2_som
deltaP_SOLIN_som=PT_SOLIN_som-PT_2CO2_som

deltaP_1CO2_sst=PT_1CO2_sst-PT_2CO2_sst
deltaP_UNIF_sst=PT_UNIF_sst-PT_2CO2_sst
deltaP_SOLIN_sst=PT_SOLIN_sst-PT_2CO2_sst

deltaTS_1CO2_som=TS_1CO2_som-TS_2CO2_som
deltaTS_UNIF_som=TS_UNIF_som-TS_2CO2_som
deltaTS_SOLIN_som=TS_SOLIN_som-TS_2CO2_som

deltaTS_1CO2_sst=TS_1CO2_sst-TS_2CO2_sst
deltaTS_UNIF_sst=TS_UNIF_sst-TS_2CO2_sst
deltaTS_SOLIN_sst=TS_SOLIN_sst-TS_2CO2_sst

PT_mean=c((mean(deltaP_1CO2_som)/mean(PT_1CO2_som))*100,
(mean(deltaP_1CO2_sst)/mean(PT_1CO2_sst))*100,
(mean(deltaP_UNIF_som)/mean(PT_1CO2_som))*100,
(mean(deltaP_UNIF_sst)/mean(PT_1CO2_sst))*100,
(mean(deltaP_SOLIN_som)/mean(PT_1CO2_som))*100,
(mean(deltaP_SOLIN_sst)/mean(PT_1CO2_sst))*100)

TS_mean=c(mean(deltaTS_1CO2_som),
mean(deltaTS_1CO2_sst),
mean(deltaTS_UNIF_som),
mean(deltaTS_UNIF_sst),
mean(deltaTS_SOLIN_som),
mean(deltaTS_SOLIN_sst))

df=data.frame(Experiment=c("1CO2","1CO2","SAI","SAI","SOLAR","SOLAR"),
Cntl= c("SOM","SST","SOM","SST","SOM","SST"),
PT_mean,
PT_SE=c(standard_error(deltaP_1CO2_som/PT_1CO2_som)*100,
standard_error(deltaP_1CO2_sst/PT_1CO2_sst)*100,
standard_error(deltaP_UNIF_som/PT_1CO2_som)*100,
standard_error(deltaP_UNIF_sst/PT_1CO2_sst)*100,
standard_error(deltaP_SOLIN_som/PT_1CO2_som)*100,
standard_error(deltaP_SOLIN_sst/PT_1CO2_sst)*100),
TS_mean,
TS_SE=c(standard_error(deltaTS_1CO2_som),
standard_error(deltaTS_1CO2_sst),
standard_error(deltaTS_UNIF_som),
standard_error(deltaTS_UNIF_sst),
standard_error(deltaTS_SOLIN_som),
standard_error(deltaTS_SOLIN_sst)))

print(df)

linea=(df$PT_mean[2]-df$PT_mean[1])/(df$TS_mean[2]-df$TS_mean[1])
slopea=(df$PT_SE[2]-df$PT_SE[1])/(df$TS_SE[2]-df$TS_SE[1])

lineb=(df$PT_mean[4]-df$PT_mean[3])/(df$TS_mean[4]-df$TS_mean[3])
slopeb=(df$PT_SE[4]-df$PT_SE[3])/(df$TS_SE[4]-df$TS_SE[3])

linec=(df$PT_mean[6]-df$PT_mean[5])/(df$TS_mean[6]-df$TS_mean[5])
slopec=(df$PT_SE[6]-df$PT_SE[5])/(df$TS_SE[6]-df$TS_SE[5])

# Commented out as df only has 6 rows
# lined=(df$PT_mean[8]-df$PT_mean[7])/(df$TS_mean[8]-df$TS_mean[7])
# sloped=(df$PT_SE[8]-df$PT_SE[7])/(df$TS_SE[8]-df$TS_SE[7])
# linea=(df$PT_mean[1])/(df$TS_mean[1])
# slopea=(df$PT_SE[1]-df$PT_SE[2])/(df$TS_SE[1])

# lineb=(df$PT_mean[3])/(df$TS_mean[3])
# slopeb=(df$PT_SE[3])/(df$TS_SE[3])

# linec=(df$PT_mean[5])/(df$TS_mean[5])
# slopec=(df$PT_SE[5])/(df$TS_SE[5])
# print(lined)
# print(sloped)
line <- lm(df$TS_mean[c(1,2)]~ df$PT_mean[c(1,2)])
int <- summary(line)$coefficients[1]
slope <- summary(line)$coefficients[2]
Label_line_equation=paste0("y = ",round(slope,2), "x + ",round(int,2))
print(Label_line_equation)

line1 <- lm(df$TS_mean[c(3,4)]~ df$PT_mean[c(3,4)])
int1 <- summary(line1)$coefficients[1]
slope1 <- summary(line1)$coefficients[2]
Label_line_equation1=paste0("y = ",round(slope1,2), "x + ",round(int1,2))
print(Label_line_equation1)


line2 <- lm(df$TS_mean[c(5,6)]~ df$PT_mean[c(5,6)])
int2 <- summary(line2)$coefficients[1]
slope2 <- summary(line2)$coefficients[2]
Label_line_equation2=paste0("y = ",round(slope2,2), "x + ",round(int2,2))
print(Label_line_equation2)


# line3 <- lm(df$TS_mean[c(7,8)]~ df$PT_mean[c(7,8)])
# int3 <- summary(line3)$coefficients[1]
# slope3 <- summary(line3)$coefficients[2]
# Label_line_equation3=paste0("y = ",round(slope3,2), "x + ",round(int3,2))
# print(Label_line_equation3)

experiment_colors <- c("1CO2" = "red", "SAI" = "blue", "SOLAR" = "green")
# exp_shapes <- c(16,15,17,18,13,8)
exp_shapes <- c(15,17)
p <- ggplot(df, aes(x =TS_mean, y =PT_mean)) +

  theme_bw()+
# geom_point(size=3)+
geom_point(data = df[c(1:2),],aes( shape = Cntl,color = Experiment), size = 6) +
geom_point(data = df[c(3:4),],aes( shape = Cntl,color = Experiment), size = 6) +
geom_point(data = df[c(5:6),],aes( shape = Cntl,color = Experiment), size = 6) +
# geom_point(data = df[c(7:8),],aes( shape = Cntl,color = Experiment), size = 6) +
#geom_point(data = df[c(1:4),],aes( shape = Cntl,color = Experiment), size = 6) +
# geom_point(data = df[c(1:8),],aes( shape = Cntl,color = Experiment), size = 6) +

guides(shape = guide_legend(override.aes = list(shape= c(15,17))))+
guides(color = guide_legend(override.aes = list(shape = c(95,95,95),size=8) ))+

geom_smooth(data=df[c(1:2),],aes(x = TS_mean, y = PT_mean),method = "lm", color ="red",lty=1, linewidth = 1, level = 0.95, se = F) +
geom_smooth(data=df[c(3:4),],aes(x = TS_mean, y = PT_mean),method = "lm", color ="blue",lty=1, linewidth = 1.7, level = 0.95, se = F) +
geom_smooth(data=df[c(5:6),],aes(x = TS_mean, y = PT_mean),method = "lm", color ="green",lty=1, linewidth = 1.7, level = 0.95, se = F) +
# geom_smooth(data=df[c(7:8),],aes(x = TS_mean, y = PT_mean),method = "lm", color ="orange",lty=1, linewidth = 1.7, level = 0.95, se = F) +

geom_vline(xintercept = 0, linetype = "dashed", color = "black") +  # Vertical line at x = 0
  geom_hline(yintercept = 0, linetype = "dashed", color = "black") +  # Horizontal line at y = 0
  labs(x = "Temperature (K)", y = "Precipitation (%)", color = "Experiment", shape = "Control") +
    scale_color_manual(values = experiment_colors)+
  scale_shape_manual(values = exp_shapes)+
  # scale_color_brewer(palette="Paired")+
  scale_y_continuous(name = expression(~Delta~"Precipitation" ~ "(%)"), breaks = seq(-15,3,3), limits = c(-15.5, 3.5))+
  scale_x_continuous(name = expression(~ Delta~"Temperature (K)"), breaks = seq(-6,0.3,1), limits = c(-6.5, 0.3))+
     theme(axis.title.x = element_text(size = 22)) + # Increase the font size of the x-axis label
      theme(axis.title.y = element_text(size = 22)) +
      theme(plot.title = element_text(size = 22)) +
     theme(axis.text.x = element_text(size = 22)) + # Increase the font size of the x-axis label
      theme(axis.text.y = element_text(size = 22)) +
  ggtitle("Hydrological sensitivity")
    print(p)
  # p+ geom_smooth(data=df[c(1, 2),],aes(x = TS_mean, y = PT_mean),method = "lm", color ="red",lty=1, linewidth = 1.7, level = 0.95, se = F)

    # p + geom_text(aes(x = 1, y = 1, label = "y = mx + c"), color = "red", size = 4)
  # ggsave("TS_PT.png", width = 20, height = 15, units = "cm", dpi = 300)
    # ggsave("AHS_TS_PT_POLAR.png", width = 20, height = 15, units = "cm", dpi = 300)
  #ggsave("HS_TS_PT_CSG.png", width = 20, height = 15, units = "cm", dpi = 300)
    ggsave("HS_TS_PT_CSG_1.png", width = 20, height = 15, units = "cm", dpi = 300)

# p