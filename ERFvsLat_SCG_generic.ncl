load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************


; e_path="/Volumes/Backup Plus"
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")

e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/E2000_solin/Yearly_E2000_solin_01_100_1.nc", "r")





f_1CO2 = addfile(e_path+"/Yearly_F_CO2_01_60_new1.nc", "r")
f_2CO2 = addfile(e_path+"/Yearly_F_2CO2_01_60_new1.nc", "r")

f_UNIF_22_5 = addfile(e_path+"/Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc", "r")
f_SOLAR = addfile("/home/<USER>/Documents/Data_solin/F2000_solin/Yearly_F2000_solin_01_60_1.nc", "r")



NET_RAD_SST_1CO2          = (/f_1CO2->FSNT(30:59,:,:) - f_1CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_2CO2          = (/f_2CO2->FSNT(30:59,:,:) - f_2CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_UNIF_22_5          = (/f_UNIF_22_5->FSNT(30:59,:,:) - f_UNIF_22_5->FLNT(30:59,:,:)/)
NET_RAD_SST_SOLAR          = (/f_SOLAR->FSNT(30:59,:,:) - f_SOLAR->FLNT(30:59,:,:)/)


NET_RAD_SOM_1CO2          = (/e_1CO2->FSNT(40:99,:,:) - e_1CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_2CO2          = (/e_2CO2->FSNT(40:99,:,:) - e_2CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_UNIF_22_5          = (/e_UNIF_22_5->FSNT(40:99,:,:) - e_UNIF_22_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_SOLAR          = (/e_SOLAR->FSNT(40:99,:,:) - e_SOLAR->FLNT(40:99,:,:)/)

;---------------------------------------------------------
dummy1=f_1CO2->TS(:,:,:)
dummy2=e_1CO2->TS(:,:,:)

copy_VarCoords(dummy1,NET_RAD_SST_1CO2)
copy_VarCoords(dummy1,NET_RAD_SST_2CO2)
copy_VarCoords(dummy1,NET_RAD_SST_UNIF_22_5)
copy_VarCoords(dummy1,NET_RAD_SST_SOLAR)



copy_VarCoords(dummy2,NET_RAD_SOM_1CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_2CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_UNIF_22_5)
copy_VarCoords(dummy2,NET_RAD_SOM_SOLAR)

;******************************************                 Areal average
lat=f_1CO2->lat
lon=f_1CO2->lon

  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************
dummy3=f_1CO2->lat
copy_VarCoords(dummy3,area)
;print(area)
dummy4=e_1CO2->TS(0,:,:)
; Prepare for area-weighted averaging

; Calculate weights for area-weighted averaging
; Weighting across longitude (cos(lat) is constant across longitude, so apply across lon)
rad = 4.0*atan(1.0)/180.0  ; Convert degrees to radians
clat = cos(lat * rad)   ; latitude weights
weights = conform_dims((/dimsizes(lat), dimsizes(lon)/), clat, 0)

; Average over time and longitude to get zonal mean forcing
; Time average first for NET_RAD
NET_RAD_SST_2CO2_avg = dim_avg_n_Wrap(NET_RAD_SST_2CO2, 0)  ; [lat, lon]
NET_RAD_SST_1CO2_avg = dim_avg_n_Wrap(NET_RAD_SST_1CO2, 0)
NET_RAD_SST_UNIF_avg = dim_avg_n_Wrap(NET_RAD_SST_UNIF_22_5, 0)
NET_RAD_SST_SOLAR_avg = dim_avg_n_Wrap(NET_RAD_SST_SOLAR, 0)

; Weighted zonal mean for NET_RAD: average across lon (dim 1)
NET_RAD_SST_2CO2_zonal = dim_avg_n_Wrap(NET_RAD_SST_2CO2_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
NET_RAD_SST_1CO2_zonal = dim_avg_n_Wrap(NET_RAD_SST_1CO2_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
NET_RAD_SST_UNIF_zonal = dim_avg_n_Wrap(NET_RAD_SST_UNIF_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
NET_RAD_SST_SOLAR_zonal = dim_avg_n_Wrap(NET_RAD_SST_SOLAR_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)

; NET_RAD difference vs latitude
delta_N_SST_1CO2_lat = NET_RAD_SST_2CO2_zonal - NET_RAD_SST_1CO2_zonal  ; 2CO2 - 1CO2
delta_N_SST_UNIF_22_5_lat = NET_RAD_SST_UNIF_zonal - NET_RAD_SST_2CO2_zonal  ; UNIF - 2CO2
delta_N_SST_SOLAR_lat = NET_RAD_SST_SOLAR_zonal - NET_RAD_SST_2CO2_zonal  ; SOLAR - 2CO2
delta_N_SST_UNIF_22_5_lat1 = NET_RAD_SST_UNIF_zonal - NET_RAD_SST_1CO2_zonal  ; UNIF - 1CO2
delta_N_SST_SOLAR_lat1 = NET_RAD_SST_SOLAR_zonal - NET_RAD_SST_1CO2_zonal  ; SOLAR - 1CO2
printVarSummary(delta_N_SST_UNIF_22_5_lat)
; Time average first
TS_2CO2_avg = dim_avg_n_Wrap(f_2CO2->TS(30:59,:,:), 0)  ; [lat, lon]
TS_1CO2_avg = dim_avg_n_Wrap(f_1CO2->TS(30:59,:,:), 0)
TS_UNIF_avg = dim_avg_n_Wrap(f_UNIF_22_5->TS(30:59,:,:), 0)
TS_SOLAR_avg = dim_avg_n_Wrap(f_SOLAR->TS(30:59,:,:), 0)

; Weighting across longitude (cos(lat) is constant across longitude, so apply across lon)
clat = cos(lat * 0.01745329)         ; radians
weights = conform_dims((/dimsizes(lat), dimsizes(lon)/), clat, 0)

; Weighted zonal mean: average across lon (dim 1)
TS_2CO2_zonal = dim_avg_n_Wrap(TS_2CO2_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
TS_1CO2_zonal = dim_avg_n_Wrap(TS_1CO2_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
TS_UNIF_zonal = dim_avg_n_Wrap(TS_UNIF_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
TS_SOLAR_zonal = dim_avg_n_Wrap(TS_SOLAR_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)

; Temperature difference vs latitude
DELTA_T_SST_1CO2_lat = TS_2CO2_zonal - TS_1CO2_zonal  ; 2CO2 - 1CO2
DELTA_T_SST_UNIF_22_5_lat = TS_UNIF_zonal - TS_2CO2_zonal  ; UNIF - 2CO2
DELTA_T_SST_SOLAR_lat = TS_SOLAR_zonal - TS_2CO2_zonal  ; SOLAR - 2CO2
DELTA_T_SST_UNIF_22_5_lat1 = TS_UNIF_zonal - TS_1CO2_zonal  ; UNIF - 1CO2
DELTA_T_SST_SOLAR_lat1 = TS_SOLAR_zonal - TS_1CO2_zonal  ; SOLAR - 1CO2

printVarSummary(DELTA_T_SST_1CO2_lat)
DELTA_N_SST_1CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1))
DELTA_N_SST_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_SOLAR_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_SOLAR,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_UNIF_GLOBALmean_22_5_1 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1))
DELTA_N_SST_SOLAR_GLOBALmean_22_5_1 = (wgt_areaave_Wrap(NET_RAD_SST_SOLAR,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1))

DELTA_T_SST_1CO2_GLOBALmean = (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_SOLAR_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_UNIF_GLOBALmean_22_5_1 = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_SOLAR_GLOBALmean_22_5_1 = (wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1))

DELTA_N_SOM_1CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1))
DELTA_N_SOM_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_SOLAR_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_SOLAR,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_UNIF_GLOBALmean_22_5_1 = (wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1))
DELTA_N_SOM_SOLAR_GLOBALmean_22_5_1 = (wgt_areaave_Wrap(NET_RAD_SOM_SOLAR,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1))

DELTA_T_SOM_1CO2_GLOBALmean = (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_SOLAR_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_SOLAR->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_UNIF_GLOBALmean_22_5_1 = (wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_SOLAR_GLOBALmean_22_5_1 = (wgt_areaave_Wrap(e_SOLAR->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1))


Lamda_1CO2 = (dim_avg_n_Wrap(DELTA_N_SST_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_1CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SOM_1CO2_GLOBALmean, 0))
Lamda_UNIF_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0))
Lamda_SOLAR = (dim_avg_n_Wrap(DELTA_N_SST_SOLAR_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_SOLAR_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_SOLAR_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_SOLAR_GLOBALmean_22_5, 0))
Lamda_UNIF_22_5_1 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5_1, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5_1, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5_1, 0) - dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5_1, 0))
Lamda_SOLAR_1 = (dim_avg_n_Wrap(DELTA_N_SST_SOLAR_GLOBALmean_22_5_1, 0) - dim_avg_n_Wrap(DELTA_N_SOM_SOLAR_GLOBALmean_22_5_1, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_SOLAR_GLOBALmean_22_5_1, 0) - dim_avg_n_Wrap(DELTA_T_SOM_SOLAR_GLOBALmean_22_5_1, 0))


;ERF_1CO2 = dim_avg_n_Wrap((delta_N_SST_1CO2_lat - (Lamda_1CO2 * DELTA_T_SST_1CO2_lat)),0)
;ERF_UNIF_22_5 = dim_avg_n_Wrap((delta_N_SST_UNIF_22_5_lat - (Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5_lat)),0)
;ERF_SOLAR = dim_avg_n_Wrap((DELTA_N_SST_SOLAR - (Lamda_SOLAR * DELTA_T_SST_SOLAR)),0)
FillValue=-999.999
printVarSummary(delta_N_SST_1CO2_lat)
printVarSummary(Lamda_1CO2)
printVarSummary(DELTA_T_SST_1CO2_lat)
print(Lamda_1CO2)
ERF=new((/5,96/),double) 
ERF(0,:) = delta_N_SST_1CO2_lat - (todouble(Lamda_1CO2) * DELTA_T_SST_1CO2_lat)
ERF(1,:)= delta_N_SST_UNIF_22_5_lat - (todouble(Lamda_UNIF_22_5) * DELTA_T_SST_UNIF_22_5_lat)

; For SOLAR-2CO2
ERF(2,:)= delta_N_SST_SOLAR_lat - (todouble(Lamda_SOLAR) * DELTA_T_SST_SOLAR_lat)
; For UNIF-1CO2 (using the same lambda as UNIF-2CO2 for now)
ERF(3,:)= delta_N_SST_UNIF_22_5_lat1 - (todouble(Lamda_UNIF_22_5) * DELTA_T_SST_UNIF_22_5_lat1)

; For SOLAR-1CO2 (using the same lambda as SOLAR-2CO2 for now)
ERF(4,:)= delta_N_SST_SOLAR_lat1 - (todouble(Lamda_SOLAR )* DELTA_T_SST_SOLAR_lat1)

; Calculate UNIF ERF relative to 1CO2
delta_N_SST_UNIF_1CO2_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_UNIF_22_5, 2), 0) - dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_1CO2, 2), 0)
DELTA_T_SST_UNIF_1CO2_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(f_UNIF_22_5->TS(30:59,:,:),2),0) - dim_avg_n_Wrap(dim_avg_n_Wrap(f_1CO2->TS(30:59,:,:),2),0)

; Calculate SOLAR ERF relative to 1CO2
delta_N_SST_SOLAR_1CO2_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_SOLAR, 2), 0) - dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_1CO2, 2), 0)
DELTA_T_SST_SOLAR_1CO2_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(f_SOLAR->TS(30:59,:,:),2),0) - dim_avg_n_Wrap(dim_avg_n_Wrap(f_1CO2->TS(30:59,:,:),2),0)


deg2rad = 4.0 * atan(1.0) / 180.0
coslat  = cos(lat * deg2rad)

; For ERF_1CO2
ERF_zonal_1CO2 = delta_N_SST_1CO2_lat - (todouble(Lamda_1CO2) * DELTA_T_SST_1CO2_lat) ;Lamda_1CO2 * DELTA_T_SST_1CO2_lat
ERF_1CO2 = dim_sum_n(ERF_zonal_1CO2 * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_UNIF_22_5
ERF_zonal_UNIF_22_5 = delta_N_SST_UNIF_22_5_lat - (todouble(Lamda_UNIF_22_5) * DELTA_T_SST_UNIF_22_5_lat) ;Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5_lat
ERF_UNIF_22_5 = dim_sum_n(ERF_zonal_UNIF_22_5 * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_SOLAR
ERF_zonal_SOLAR = delta_N_SST_SOLAR_lat - (todouble(Lamda_SOLAR) * DELTA_T_SST_SOLAR_lat) ;Lamda_SOLAR * DELTA_T_SST_SOLAR_lat
ERF_SOLAR = dim_sum_n(ERF_zonal_SOLAR  * coslat, 0) / dim_sum_n(coslat, 0)

ERF1 = new((/3, dimsizes(lat)/), double)
ERF1(0,:) = ERF_zonal_1CO2
ERF1(1,:) = ERF_zonal_UNIF_22_5
ERF1!0 = "case"
ERF1!1 = "lat"
ERF1&lat = lat
ERF1(2,:) = ERF_zonal_SOLAR ; Fixed variable name


; ERF_1CO2_1 = dim_avg_n_Wrap((DELTA_N_SST_1CO2(30:59) - (Lamda_1CO2 * DELTA_T_SST_1CO2(30:59))),0)

;  print(Label_Global_mean)
;------------------------------------------------
; asciiwrite("Change_AOD_mean_rectangular_grid.txt",Label_Global_mean)
; asciiwrite("Change_AOD_mean_rectangular_grid_2point.txt",Label_Global_mean1)
; asciiwrite("Change_AOD_mean_rectangular_grid_all.txt",Global_mean)
; print(Lamda_UNIF_22_5)
print(ERF)
print(ERF1)
;print(DELTA_T_SST_1CO2_lat)
; print((dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_15, 0)))
;print((dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0)))

; asciiwrite("ERF_globalmean_rectangular_grid_all.txt",ERF)
;asciiwrite("ERF_globalmean_rectangular_grid_all_new_2lev.txt",ERF)

;plotting
;***********************************************************
; Plot: Radiative Forcing vs Latitude
;***********************************************************
wks = gsn_open_wks("pdf","ERF_vs_lat_SCG_area_weighted")   ; or "png" instead of "pdf"

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"red","blue","green","blue","green"/)
res@xyLineThicknesses = (/2.5, 2.5, 2.5, 2.5, 2.5/)
res@xyDashPatterns    = (/0, 0,0,1,1/)         ; solid and dashed
res@gsnXYTopLabel     = False
res@tiMainString      = "Effective Radiative Forcing vs Latitude (Area-Weighted)"
res@tiYAxisString     = "ERF (Wm~S~-2~NN~)"
res@tiXAxisString     = "Latitude (~F34~0~F~)"
res@trXMinF           = -90
res@trXMaxF           = 90

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"1xCO~B~2", "SAI","SOLAR","SAI+CO~B~2","SOLAR+CO~B~2"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.18                       ;-- define legend width
res@pmLegendHeightF        =  0.11
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

plot = gsn_csm_xy(wks, lat, ERF, res)

  exit()

end



