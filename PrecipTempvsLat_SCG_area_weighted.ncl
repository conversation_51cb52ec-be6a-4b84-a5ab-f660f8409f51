load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; Define paths and load data
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/E2000_solin/Yearly_E2000_solin_01_100_1.nc", "r")

f_1CO2 = addfile(e_path+"/Yearly_F_CO2_01_60_new1.nc", "r")
f_2CO2 = addfile(e_path+"/Yearly_F_2CO2_01_60_new1.nc", "r")
f_UNIF_22_5 = addfile(e_path+"/Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc", "r")
f_SOLAR = addfile("/home/<USER>/Documents/Data_solin/F2000_solin/Yearly_F2000_solin_01_60_1.nc", "r")

; Get latitude and longitude
lat = f_1CO2->lat
lon = f_1CO2->lon

; Create weights for area-weighted averaging
rad = 4.0*atan(1.0)/180.0
clat = cos(lat*rad)  ; cosine of latitude for area weighting

; Calculate zonal means for precipitation (PRECC + PRECL) with proper area weighting
; SST runs - precipitation
; For 2CO2-1CO2
PRECIP_SST_2CO2_time_avg = dim_avg_n_Wrap((f_2CO2->PRECC(30:59,:,:) + f_2CO2->PRECL(30:59,:,:))*86400*1000, 0)  ; Convert to mm/day
PRECIP_SST_1CO2_time_avg = dim_avg_n_Wrap((f_1CO2->PRECC(30:59,:,:) + f_1CO2->PRECL(30:59,:,:))*86400*1000, 0)
delta_PRECIP_SST_2CO2_lat = dim_avg_n_Wrap(PRECIP_SST_2CO2_time_avg, 1) - dim_avg_n_Wrap(PRECIP_SST_1CO2_time_avg, 1)

; For UNIF-2CO2
PRECIP_SST_UNIF_22_5_time_avg = dim_avg_n_Wrap((f_UNIF_22_5->PRECC(30:59,:,:) + f_UNIF_22_5->PRECL(30:59,:,:))*86400*1000, 0)
delta_PRECIP_SST_UNIF_22_5_lat = dim_avg_n_Wrap(PRECIP_SST_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(PRECIP_SST_2CO2_time_avg, 1)

; For SOLAR-2CO2
PRECIP_SST_SOLAR_time_avg = dim_avg_n_Wrap((f_SOLAR->PRECC(30:59,:,:) + f_SOLAR->PRECL(30:59,:,:))*86400*1000, 0)
delta_PRECIP_SST_SOLAR_lat = dim_avg_n_Wrap(PRECIP_SST_SOLAR_time_avg, 1) - dim_avg_n_Wrap(PRECIP_SST_2CO2_time_avg, 1)

; For UNIF-1CO2
delta_PRECIP_SST_UNIF_1CO2_lat = dim_avg_n_Wrap(PRECIP_SST_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(PRECIP_SST_1CO2_time_avg, 1)

; For SOLAR-1CO2
delta_PRECIP_SST_SOLAR_1CO2_lat = dim_avg_n_Wrap(PRECIP_SST_SOLAR_time_avg, 1) - dim_avg_n_Wrap(PRECIP_SST_1CO2_time_avg, 1)

; SOM runs - precipitation
; For 2CO2-1CO2
PRECIP_SOM_2CO2_time_avg = dim_avg_n_Wrap((e_2CO2->PRECC(40:99,:,:) + e_2CO2->PRECL(40:99,:,:))*86400*1000, 0)
PRECIP_SOM_1CO2_time_avg = dim_avg_n_Wrap((e_1CO2->PRECC(40:99,:,:) + e_1CO2->PRECL(40:99,:,:))*86400*1000, 0)
delta_PRECIP_SOM_2CO2_lat = dim_avg_n_Wrap(PRECIP_SOM_2CO2_time_avg, 1) - dim_avg_n_Wrap(PRECIP_SOM_1CO2_time_avg, 1)

; For UNIF-2CO2
PRECIP_SOM_UNIF_22_5_time_avg = dim_avg_n_Wrap((e_UNIF_22_5->PRECC(40:99,:,:) + e_UNIF_22_5->PRECL(40:99,:,:))*86400*1000, 0)
delta_PRECIP_SOM_UNIF_22_5_lat = dim_avg_n_Wrap(PRECIP_SOM_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(PRECIP_SOM_2CO2_time_avg, 1)

; For SOLAR-2CO2
PRECIP_SOM_SOLAR_time_avg = dim_avg_n_Wrap((e_SOLAR->PRECC(40:99,:,:) + e_SOLAR->PRECL(40:99,:,:))*86400*1000, 0)
delta_PRECIP_SOM_SOLAR_lat = dim_avg_n_Wrap(PRECIP_SOM_SOLAR_time_avg, 1) - dim_avg_n_Wrap(PRECIP_SOM_2CO2_time_avg, 1)

; For UNIF-1CO2
delta_PRECIP_SOM_UNIF_1CO2_lat = dim_avg_n_Wrap(PRECIP_SOM_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(PRECIP_SOM_1CO2_time_avg, 1)

; For SOLAR-1CO2
delta_PRECIP_SOM_SOLAR_1CO2_lat = dim_avg_n_Wrap(PRECIP_SOM_SOLAR_time_avg, 1) - dim_avg_n_Wrap(PRECIP_SOM_1CO2_time_avg, 1)

; Calculate zonal means for surface temperature with proper area weighting
; SST runs - temperature
; For 2CO2-1CO2
TS_SST_2CO2_time_avg = dim_avg_n_Wrap(f_2CO2->TS(30:59,:,:), 0)
TS_SST_1CO2_time_avg = dim_avg_n_Wrap(f_1CO2->TS(30:59,:,:), 0)
delta_TS_SST_2CO2_lat = dim_avg_n_Wrap(TS_SST_2CO2_time_avg, 1) - dim_avg_n_Wrap(TS_SST_1CO2_time_avg, 1)

; For UNIF-2CO2
TS_SST_UNIF_22_5_time_avg = dim_avg_n_Wrap(f_UNIF_22_5->TS(30:59,:,:), 0)
delta_TS_SST_UNIF_22_5_lat = dim_avg_n_Wrap(TS_SST_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(TS_SST_2CO2_time_avg, 1)

; For SOLAR-2CO2
TS_SST_SOLAR_time_avg = dim_avg_n_Wrap(f_SOLAR->TS(30:59,:,:), 0)
delta_TS_SST_SOLAR_lat = dim_avg_n_Wrap(TS_SST_SOLAR_time_avg, 1) - dim_avg_n_Wrap(TS_SST_2CO2_time_avg, 1)

; For UNIF-1CO2
delta_TS_SST_UNIF_1CO2_lat = dim_avg_n_Wrap(TS_SST_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(TS_SST_1CO2_time_avg, 1)

; For SOLAR-1CO2
delta_TS_SST_SOLAR_1CO2_lat = dim_avg_n_Wrap(TS_SST_SOLAR_time_avg, 1) - dim_avg_n_Wrap(TS_SST_1CO2_time_avg, 1)

; SOM runs - temperature
; For 2CO2-1CO2
TS_SOM_2CO2_time_avg = dim_avg_n_Wrap(e_2CO2->TS(40:99,:,:), 0)
TS_SOM_1CO2_time_avg = dim_avg_n_Wrap(e_1CO2->TS(40:99,:,:), 0)
delta_TS_SOM_2CO2_lat = dim_avg_n_Wrap(TS_SOM_2CO2_time_avg, 1) - dim_avg_n_Wrap(TS_SOM_1CO2_time_avg, 1)

; For UNIF-2CO2
TS_SOM_UNIF_22_5_time_avg = dim_avg_n_Wrap(e_UNIF_22_5->TS(40:99,:,:), 0)
delta_TS_SOM_UNIF_22_5_lat = dim_avg_n_Wrap(TS_SOM_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(TS_SOM_2CO2_time_avg, 1)

; For SOLAR-2CO2
TS_SOM_SOLAR_time_avg = dim_avg_n_Wrap(e_SOLAR->TS(40:99,:,:), 0)
delta_TS_SOM_SOLAR_lat = dim_avg_n_Wrap(TS_SOM_SOLAR_time_avg, 1) - dim_avg_n_Wrap(TS_SOM_2CO2_time_avg, 1)

; For UNIF-1CO2
delta_TS_SOM_UNIF_1CO2_lat = dim_avg_n_Wrap(TS_SOM_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(TS_SOM_1CO2_time_avg, 1)

; For SOLAR-1CO2
delta_TS_SOM_SOLAR_1CO2_lat = dim_avg_n_Wrap(TS_SOM_SOLAR_time_avg, 1) - dim_avg_n_Wrap(TS_SOM_1CO2_time_avg, 1)

; Calculate global mean values with proper area weighting
; Precipitation global means - SST
DELTA_PRECIP_SST_2CO2_GLOBALmean = wgt_areaave_Wrap((f_2CO2->PRECC(30:59,:,:) + f_2CO2->PRECL(30:59,:,:))*86400*1000,clat, 1.0, 0) - wgt_areaave_Wrap((f_1CO2->PRECC(30:59,:,:) + f_1CO2->PRECL(30:59,:,:))*86400*1000,clat, 1.0, 0)
DELTA_PRECIP_SST_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap((f_UNIF_22_5->PRECC(30:59,:,:) + f_UNIF_22_5->PRECL(30:59,:,:))*86400*1000,clat, 1.0, 0) - wgt_areaave_Wrap((f_2CO2->PRECC(30:59,:,:) + f_2CO2->PRECL(30:59,:,:))*86400*1000,clat, 1.0, 0)
DELTA_PRECIP_SST_SOLAR_GLOBALmean = wgt_areaave_Wrap((f_SOLAR->PRECC(30:59,:,:) + f_SOLAR->PRECL(30:59,:,:))*86400*1000,clat, 1.0, 0) - wgt_areaave_Wrap((f_2CO2->PRECC(30:59,:,:) + f_2CO2->PRECL(30:59,:,:))*86400*1000,clat, 1.0, 0)
DELTA_PRECIP_SST_UNIF_1CO2_GLOBALmean = wgt_areaave_Wrap((f_UNIF_22_5->PRECC(30:59,:,:) + f_UNIF_22_5->PRECL(30:59,:,:))*86400*1000,clat, 1.0, 0) - wgt_areaave_Wrap((f_1CO2->PRECC(30:59,:,:) + f_1CO2->PRECL(30:59,:,:))*86400*1000,clat, 1.0, 0)
DELTA_PRECIP_SST_SOLAR_1CO2_GLOBALmean = wgt_areaave_Wrap((f_SOLAR->PRECC(30:59,:,:) + f_SOLAR->PRECL(30:59,:,:))*86400*1000,clat, 1.0, 0) - wgt_areaave_Wrap((f_1CO2->PRECC(30:59,:,:) + f_1CO2->PRECL(30:59,:,:))*86400*1000,clat, 1.0, 0)

; Temperature global means - SST
DELTA_TS_SST_2CO2_GLOBALmean = wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),clat, 1.0, 0)
DELTA_TS_SST_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),clat, 1.0, 0)
DELTA_TS_SST_SOLAR_GLOBALmean = wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),clat, 1.0, 0)
DELTA_TS_SST_UNIF_1CO2_GLOBALmean = wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),clat, 1.0, 0)
DELTA_TS_SST_SOLAR_1CO2_GLOBALmean = wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),clat, 1.0, 0)

; Precipitation global means - SOM
DELTA_PRECIP_SOM_2CO2_GLOBALmean = wgt_areaave_Wrap((e_2CO2->PRECC(40:99,:,:) + e_2CO2->PRECL(40:99,:,:))*86400*1000,clat, 1.0, 0) - wgt_areaave_Wrap((e_1CO2->PRECC(40:99,:,:) + e_1CO2->PRECL(40:99,:,:))*86400*1000,clat, 1.0, 0)
DELTA_PRECIP_SOM_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap((e_UNIF_22_5->PRECC(40:99,:,:) + e_UNIF_22_5->PRECL(40:99,:,:))*86400*1000,clat, 1.0, 0) - wgt_areaave_Wrap((e_2CO2->PRECC(40:99,:,:) + e_2CO2->PRECL(40:99,:,:))*86400*1000,clat, 1.0, 0)
DELTA_PRECIP_SOM_SOLAR_GLOBALmean = wgt_areaave_Wrap((e_SOLAR->PRECC(40:99,:,:) + e_SOLAR->PRECL(40:99,:,:))*86400*1000,clat, 1.0, 0) - wgt_areaave_Wrap((e_2CO2->PRECC(40:99,:,:) + e_2CO2->PRECL(40:99,:,:))*86400*1000,clat, 1.0, 0)
DELTA_PRECIP_SOM_UNIF_1CO2_GLOBALmean = wgt_areaave_Wrap((e_UNIF_22_5->PRECC(40:99,:,:) + e_UNIF_22_5->PRECL(40:99,:,:))*86400*1000,clat, 1.0, 0) - wgt_areaave_Wrap((e_1CO2->PRECC(40:99,:,:) + e_1CO2->PRECL(40:99,:,:))*86400*1000,clat, 1.0, 0)
DELTA_PRECIP_SOM_SOLAR_1CO2_GLOBALmean = wgt_areaave_Wrap((e_SOLAR->PRECC(40:99,:,:) + e_SOLAR->PRECL(40:99,:,:))*86400*1000,clat, 1.0, 0) - wgt_areaave_Wrap((e_1CO2->PRECC(40:99,:,:) + e_1CO2->PRECL(40:99,:,:))*86400*1000,clat, 1.0, 0)

; Temperature global means - SOM
DELTA_TS_SOM_2CO2_GLOBALmean = wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),clat, 1.0, 0)
DELTA_TS_SOM_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),clat, 1.0, 0)
DELTA_TS_SOM_SOLAR_GLOBALmean = wgt_areaave_Wrap(e_SOLAR->TS(40:99,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),clat, 1.0, 0)
DELTA_TS_SOM_UNIF_1CO2_GLOBALmean = wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),clat, 1.0, 0)
DELTA_TS_SOM_SOLAR_1CO2_GLOBALmean = wgt_areaave_Wrap(e_SOLAR->TS(40:99,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),clat, 1.0, 0)

; Print global mean values
print("Global mean precipitation changes (mm/day):")
print("SST runs:")
print("2CO2-1CO2: " + dim_avg_n_Wrap(DELTA_PRECIP_SST_2CO2_GLOBALmean, 0))
print("UNIF-2CO2: " + dim_avg_n_Wrap(DELTA_PRECIP_SST_UNIF_GLOBALmean_22_5, 0))
print("SOLAR-2CO2: " + dim_avg_n_Wrap(DELTA_PRECIP_SST_SOLAR_GLOBALmean, 0))
print("UNIF-1CO2: " + dim_avg_n_Wrap(DELTA_PRECIP_SST_UNIF_1CO2_GLOBALmean, 0))
print("SOLAR-1CO2: " + dim_avg_n_Wrap(DELTA_PRECIP_SST_SOLAR_1CO2_GLOBALmean, 0))

print("SOM runs:")
print("2CO2-1CO2: " + dim_avg_n_Wrap(DELTA_PRECIP_SOM_2CO2_GLOBALmean, 0))
print("UNIF-2CO2: " + dim_avg_n_Wrap(DELTA_PRECIP_SOM_UNIF_GLOBALmean_22_5, 0))
print("SOLAR-2CO2: " + dim_avg_n_Wrap(DELTA_PRECIP_SOM_SOLAR_GLOBALmean, 0))
print("UNIF-1CO2: " + dim_avg_n_Wrap(DELTA_PRECIP_SOM_UNIF_1CO2_GLOBALmean, 0))
print("SOLAR-1CO2: " + dim_avg_n_Wrap(DELTA_PRECIP_SOM_SOLAR_1CO2_GLOBALmean, 0))

print("Global mean temperature changes (K):")
print("SST runs:")
print("2CO2-1CO2: " + dim_avg_n_Wrap(DELTA_TS_SST_2CO2_GLOBALmean, 0))
print("UNIF-2CO2: " + dim_avg_n_Wrap(DELTA_TS_SST_UNIF_GLOBALmean_22_5, 0))
print("SOLAR-2CO2: " + dim_avg_n_Wrap(DELTA_TS_SST_SOLAR_GLOBALmean, 0))
print("UNIF-1CO2: " + dim_avg_n_Wrap(DELTA_TS_SST_UNIF_1CO2_GLOBALmean, 0))
print("SOLAR-1CO2: " + dim_avg_n_Wrap(DELTA_TS_SST_SOLAR_1CO2_GLOBALmean, 0))

print("SOM runs:")
print("2CO2-1CO2: " + dim_avg_n_Wrap(DELTA_TS_SOM_2CO2_GLOBALmean, 0))
print("UNIF-2CO2: " + dim_avg_n_Wrap(DELTA_TS_SOM_UNIF_GLOBALmean_22_5, 0))
print("SOLAR-2CO2: " + dim_avg_n_Wrap(DELTA_TS_SOM_SOLAR_GLOBALmean, 0))
print("UNIF-1CO2: " + dim_avg_n_Wrap(DELTA_TS_SOM_UNIF_1CO2_GLOBALmean, 0))
print("SOLAR-1CO2: " + dim_avg_n_Wrap(DELTA_TS_SOM_SOLAR_1CO2_GLOBALmean, 0))

; Create arrays for plotting
PRECIP_SST = new((/5, dimsizes(lat)/), float)
PRECIP_SOM = new((/5, dimsizes(lat)/), float)
TEMP_SST = new((/5, dimsizes(lat)/), float)
TEMP_SOM = new((/5, dimsizes(lat)/), float)

; Fill arrays with zonal mean data
; Precipitation
PRECIP_SST(0,:) = delta_PRECIP_SST_2CO2_lat
PRECIP_SST(1,:) = delta_PRECIP_SST_UNIF_22_5_lat
PRECIP_SST(2,:) = delta_PRECIP_SST_SOLAR_lat
PRECIP_SST(3,:) = delta_PRECIP_SST_UNIF_1CO2_lat
PRECIP_SST(4,:) = delta_PRECIP_SST_SOLAR_1CO2_lat

PRECIP_SOM(0,:) = delta_PRECIP_SOM_2CO2_lat
PRECIP_SOM(1,:) = delta_PRECIP_SOM_UNIF_22_5_lat
PRECIP_SOM(2,:) = delta_PRECIP_SOM_SOLAR_lat
PRECIP_SOM(3,:) = delta_PRECIP_SOM_UNIF_1CO2_lat
PRECIP_SOM(4,:) = delta_PRECIP_SOM_SOLAR_1CO2_lat

; Temperature
TEMP_SST(0,:) = delta_TS_SST_2CO2_lat
TEMP_SST(1,:) = delta_TS_SST_UNIF_22_5_lat
TEMP_SST(2,:) = delta_TS_SST_SOLAR_lat
TEMP_SST(3,:) = delta_TS_SST_UNIF_1CO2_lat
TEMP_SST(4,:) = delta_TS_SST_SOLAR_1CO2_lat

TEMP_SOM(0,:) = delta_TS_SOM_2CO2_lat
TEMP_SOM(1,:) = delta_TS_SOM_UNIF_22_5_lat
TEMP_SOM(2,:) = delta_TS_SOM_SOLAR_lat
TEMP_SOM(3,:) = delta_TS_SOM_UNIF_1CO2_lat
TEMP_SOM(4,:) = delta_TS_SOM_SOLAR_1CO2_lat

;plotting
;***********************************************************
; Plot 1: Precipitation vs Latitude (SST)
;***********************************************************

wks1 = gsn_open_wks("png","Precipitation_vs_lat_SST_area_weighted")

res1 = True
res1@gsnDraw           = True
res1@gsnFrame          = True
res1@xyLineColors      = (/"red","blue","green","blue","green"/)
res1@xyLineThicknesses = (/2.5, 2.5, 2.5, 2.5, 2.5/)
res1@xyDashPatterns    = (/0, 0, 0, 2, 2/)         ; solid for first 3, dotted for last 2
res1@gsnXYTopLabel     = False
res1@tiMainString      = "Precipitation Change vs Latitude (SST runs)"
res1@tiYAxisString     = "~F34~D~F~ Precipitation (mm/day)"
res1@tiXAxisString     = "Latitude (~F34~0~F~)"
res1@trXMinF           = -90
res1@trXMaxF           = 90

res1@vpHeightF         = 0.6
res1@vpWidthF          = 0.75

res1@lgLabelFontHeightF = 0.015
res1@xyExplicitLegendLabels = (/"2xCO~B~2", "SAI-2CO2", "SOLAR-2CO2", "SAI-1CO2", "SOLAR-1CO2"/)
res1@pmLegendDisplayMode = "Always"
res1@pmLegendSide = "Top"
res1@pmLegendParallelPosF = 0.8
res1@pmLegendWidthF         =  0.25
res1@pmLegendHeightF        =  0.20
res1@pmLegendOrthogonalPosF = -0.40
res1@lgPerimOn = True

plot1 = gsn_csm_xy(wks1, lat, PRECIP_SST, res1)

;***********************************************************
; Plot 2: Precipitation vs Latitude (SOM)
;***********************************************************

wks2 = gsn_open_wks("png","Precipitation_vs_lat_SOM_area_weighted")

res2 = res1
res2@tiMainString      = "Precipitation Change vs Latitude (SOM runs)"

plot2 = gsn_csm_xy(wks2, lat, PRECIP_SOM, res2)

;***********************************************************
; Plot 3: Temperature vs Latitude (SST)
;***********************************************************

wks3 = gsn_open_wks("png","Temperature_vs_lat_SST_area_weighted")

res3 = res1
res3@tiMainString      = "Temperature Change vs Latitude (SST runs)"
res3@tiYAxisString     = "~F34~D~F~ Temperature (K)"

plot3 = gsn_csm_xy(wks3, lat, TEMP_SST, res3)

;***********************************************************
; Plot 4: Temperature vs Latitude (SOM)
;***********************************************************

wks4 = gsn_open_wks("png","Temperature_vs_lat_SOM_area_weighted")

res4 = res3
res4@tiMainString      = "Temperature Change vs Latitude (SOM runs)"

plot4 = gsn_csm_xy(wks4, lat, TEMP_SOM, res4)

;***********************************************************
; Plot 5: Combined Precipitation Plot (SST and SOM)
;***********************************************************

wks5 = gsn_open_wks("png","Precipitation_vs_lat_combined_area_weighted")

; Panel resources
panelRes = True
panelRes@gsnMaximize = True
panelRes@gsnPanelYWhiteSpacePercent = 5

; Resources for first panel (SST)
res5_p1 = res1
res5_p1@gsnDraw = False
res5_p1@gsnFrame = False
res5_p1@tiMainString = "Precipitation Change vs Latitude (SST runs)"
res5_p1@vpHeightF = 0.35

; Resources for second panel (SOM)
res5_p2 = res1
res5_p2@gsnDraw = False
res5_p2@gsnFrame = False
res5_p2@tiMainString = "Precipitation Change vs Latitude (SOM runs)"
res5_p2@vpHeightF = 0.35

; Create the plots
plot5_p1 = gsn_csm_xy(wks5, lat, PRECIP_SST, res5_p1)
plot5_p2 = gsn_csm_xy(wks5, lat, PRECIP_SOM, res5_p2)

; Create panel plot
gsn_panel(wks5, (/plot5_p1, plot5_p2/), (/2,1/), panelRes)

;***********************************************************
; Plot 6: Combined Temperature Plot (SST and SOM)
;***********************************************************

wks6 = gsn_open_wks("png","Temperature_vs_lat_combined_area_weighted")

; Resources for first panel (SST)
res6_p1 = res3
res6_p1@gsnDraw = False
res6_p1@gsnFrame = False
res6_p1@tiMainString = "Temperature Change vs Latitude (SST runs)"
res6_p1@vpHeightF = 0.35

; Resources for second panel (SOM)
res6_p2 = res3
res6_p2@gsnDraw = False
res6_p2@gsnFrame = False
res6_p2@tiMainString = "Temperature Change vs Latitude (SOM runs)"
res6_p2@vpHeightF = 0.35

; Create the plots
plot6_p1 = gsn_csm_xy(wks6, lat, TEMP_SST, res6_p1)
plot6_p2 = gsn_csm_xy(wks6, lat, TEMP_SOM, res6_p2)

; Create panel plot
gsn_panel(wks6, (/plot6_p1, plot6_p2/), (/2,1/), panelRes)

exit()

end
