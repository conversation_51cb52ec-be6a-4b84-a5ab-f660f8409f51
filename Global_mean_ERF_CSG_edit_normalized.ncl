; This script calculates the effective radiative forcing (ERF) for different CO2 scenarios
; and creates plots of the ERF vs latitude.
; The ERF is normalized by the slow change in global mean surface temperature.

load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"

begin

; Read in the data
f_1CO2 = addfile("SST_1CO2.nc", "r")
f_2CO2 = addfile("SST_2CO2.nc", "r")
f_UNIF_22_5 = addfile("SST_UNIF_22_5.nc", "r")
;f_TROP_22_5 = addfile("SST_TROP_22_5.nc", "r")

e_1CO2 = addfile("SOM_1CO2.nc", "r")
e_2CO2 = addfile("SOM_2CO2.nc", "r")
e_UNIF_22_5 = addfile("SOM_UNIF_22_5.nc", "r")
;e_TROP_22_5 = addfile("SOM_TROP_22_5.nc", "r")

; Get the latitude and longitude
lat = f_1CO2->lat
lon = f_1CO2->lon

; Get the area weights
area = f_1CO2->area

; Create a dummy variable for later use
dummy = f_1CO2->TS(0,:,:)
dummy2 = f_1CO2->TS(0,0,0)

; Calculate the net radiation for each scenario
NET_RAD_SST_1CO2 = f_1CO2->FSNT(30:59,:,:) - f_1CO2->FLNT(30:59,:,:)
NET_RAD_SST_2CO2 = f_2CO2->FSNT(30:59,:,:) - f_2CO2->FLNT(30:59,:,:)
NET_RAD_SST_UNIF_22_5 = f_UNIF_22_5->FSNT(30:59,:,:) - f_UNIF_22_5->FLNT(30:59,:,:)
;NET_RAD_SST_TROP_22_5 = f_TROP_22_5->FSNT(30:59,:,:) - f_TROP_22_5->FLNT(30:59,:,:)

NET_RAD_SOM_1CO2 = e_1CO2->FSNT(40:99,:,:) - e_1CO2->FLNT(40:99,:,:)
NET_RAD_SOM_2CO2 = e_2CO2->FSNT(40:99,:,:) - e_2CO2->FLNT(40:99,:,:)
NET_RAD_SOM_UNIF_22_5 = e_UNIF_22_5->FSNT(40:99,:,:) - e_UNIF_22_5->FLNT(40:99,:,:)
;NET_RAD_SOM_TROP_22_5 = e_TROP_22_5->FSNT(40:99,:,:) - e_TROP_22_5->FLNT(40:99,:,:)

; Calculate global mean differences with respect to 2CO2 but with reversed sign (1CO2-2CO2, UNIF-2CO2)
; Net radiation differences for SST runs (non-global mean versions for ERF calculation)
DELTA_N_SST_1CO2_2CO2 = (wgt_areaave_Wrap(NET_RAD_SST_1CO2(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))  ; 1CO2 - 2CO2
DELTA_N_SST_UNIF_2CO2 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))  ; UNIF - 2CO2
;DELTA_N_SST_TROP_2CO2 = (wgt_areaave_Wrap(NET_RAD_SST_TROP_22_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))  ; TROP - 2CO2

; Global mean versions
DELTA_N_SST_1CO2_2CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))  ; 1CO2 - 2CO2
DELTA_N_SST_UNIF_2CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))  ; UNIF - 2CO2
;DELTA_N_SST_TROP_2CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_TROP_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))  ; TROP - 2CO2

; Temperature differences for SST runs (non-global mean versions for ERF calculation)
DELTA_T_SST_1CO2_2CO2 = (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))  ; 1CO2 - 2CO2
DELTA_T_SST_UNIF_2CO2 = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))  ; UNIF - 2CO2
;DELTA_T_SST_TROP_2CO2 = (wgt_areaave_Wrap(f_TROP_22_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))  ; TROP - 2CO2

; Global mean versions
DELTA_T_SST_1CO2_2CO2_GLOBALmean = (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))  ; 1CO2 - 2CO2
DELTA_T_SST_UNIF_2CO2_GLOBALmean = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))  ; UNIF - 2CO2
;DELTA_T_SST_TROP_2CO2_GLOBALmean = (wgt_areaave_Wrap(f_TROP_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))  ; TROP - 2CO2

; Net radiation differences for SOM runs
DELTA_N_SOM_1CO2_2CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))  ; 1CO2 - 2CO2
DELTA_N_SOM_UNIF_2CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))  ; UNIF - 2CO2
;DELTA_N_SOM_TROP_2CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_TROP_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))  ; TROP - 2CO2

; Temperature differences for SOM runs
DELTA_T_SOM_1CO2_2CO2_GLOBALmean = (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))  ; 1CO2 - 2CO2
DELTA_T_SOM_UNIF_2CO2_GLOBALmean = (wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))  ; UNIF - 2CO2
;DELTA_T_SOM_TROP_2CO2_GLOBALmean = (wgt_areaave_Wrap(e_TROP_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))  ; TROP - 2CO2

; Print variable dimensions to debug the dimension mismatch
print("DELTA_T_SOM_1CO2_2CO2_GLOBALmean dimensions: ")
printVarSummary(DELTA_T_SOM_1CO2_2CO2_GLOBALmean)
print("DELTA_T_SST_1CO2_2CO2_GLOBALmean dimensions: ")
printVarSummary(DELTA_T_SST_1CO2_2CO2_GLOBALmean)
print("DELTA_T_SOM_UNIF_2CO2_GLOBALmean dimensions: ")
printVarSummary(DELTA_T_SOM_UNIF_2CO2_GLOBALmean)
print("DELTA_T_SST_UNIF_2CO2_GLOBALmean dimensions: ")
printVarSummary(DELTA_T_SST_UNIF_2CO2_GLOBALmean)

; Calculate slow response (SOM-SST) for surface temperature change
; Make sure we're working with compatible dimensions by using dim_avg_n_Wrap
DELTA_T_SLOW_1CO2_2CO2 = dim_avg_n_Wrap(DELTA_T_SOM_1CO2_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_1CO2_2CO2_GLOBALmean, 0)  ; 1CO2 - 2CO2
DELTA_T_SLOW_UNIF_2CO2 = dim_avg_n_Wrap(DELTA_T_SOM_UNIF_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_UNIF_2CO2_GLOBALmean, 0)  ; UNIF - 2CO2
;DELTA_T_SLOW_TROP_2CO2 = dim_avg_n_Wrap(DELTA_T_SOM_TROP_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_TROP_2CO2_GLOBALmean, 0)  ; TROP - 2CO2

; Calculate lambda values using the formula: λ = (ΔNSST – ΔNSOM) / (ΔTSOM – ΔTSST)
; Note: The denominator is (ΔTSOM – ΔTSST) which is different from the previous formula
Lamda_1CO2_2CO2 = (dim_avg_n_Wrap(DELTA_N_SST_1CO2_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_1CO2_2CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_1CO2_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_1CO2_2CO2_GLOBALmean, 0))  ; 1CO2 - 2CO2
Lamda_UNIF_2CO2 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_2CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_UNIF_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_UNIF_2CO2_GLOBALmean, 0))  ; UNIF - 2CO2
;Lamda_TROP_2CO2 = (dim_avg_n_Wrap(DELTA_N_SST_TROP_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_TROP_2CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_TROP_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_TROP_2CO2_GLOBALmean, 0))  ; TROP - 2CO2


; Calculate radiative forcing using the formula: F = ΔNSST + λ ΔTSST
; Note: This is using + instead of - compared to the previous formula
ERF_1CO2_2CO2 = dim_avg_n_Wrap((DELTA_N_SST_1CO2_2CO2 + (Lamda_1CO2_2CO2 * DELTA_T_SST_1CO2_2CO2)),0)  ; 1CO2 - 2CO2
ERF_UNIF_2CO2 = dim_avg_n_Wrap((DELTA_N_SST_UNIF_2CO2 + (Lamda_UNIF_2CO2 * DELTA_T_SST_UNIF_2CO2)),0)  ; UNIF - 2CO2
;ERF_TROP_2CO2 = dim_avg_n_Wrap((DELTA_N_SST_TROP_2CO2 + (Lamda_TROP_2CO2 * DELTA_T_SST_TROP_2CO2)),0)  ; TROP - 2CO2

; Create array for ERF values
ERF=new((/2/),typeof(dummy2),dummy2@_FillValue)
ERF(0) = dim_avg_n_Wrap((DELTA_N_SST_1CO2_2CO2 + (Lamda_1CO2_2CO2 * DELTA_T_SST_1CO2_2CO2)),0)  ; 1CO2 - 2CO2
ERF(1) = dim_avg_n_Wrap((DELTA_N_SST_UNIF_2CO2 + (Lamda_UNIF_2CO2 * DELTA_T_SST_UNIF_2CO2)),0)  ; UNIF - 2CO2
;ERF(2) = dim_avg_n_Wrap((DELTA_N_SST_TROP_2CO2 + (Lamda_TROP_2CO2 * DELTA_T_SST_TROP_2CO2)),0)  ; TROP - 2CO2

; Alternative radiative forcing calculation using slow response: F = λ × (TSOM - TSST)
; This is consistent with the main formula F = ΔNSST + λ ΔTSST
RF_SLOW=new((/2/),typeof(dummy2),dummy2@_FillValue)
RF_SLOW(0) = Lamda_1CO2_2CO2 * DELTA_T_SLOW_1CO2_2CO2  ; 1CO2 - 2CO2, using positive lambda
RF_SLOW(1) = Lamda_UNIF_2CO2 * DELTA_T_SLOW_UNIF_2CO2  ; UNIF - 2CO2, using positive lambda
;RF_SLOW(2) = Lamda_TROP_2CO2 * DELTA_T_SLOW_TROP_2CO2  ; TROP - 2CO2, using positive lambda

; Calculate RF by latitude for plotting
; First create temporary variables with proper _FillValue attributes
temp_SOM_1CO2 = e_1CO2->TS(40:99,:,:)
temp_SOM_2CO2 = e_2CO2->TS(40:99,:,:)
temp_SST_1CO2 = f_1CO2->TS(30:59,:,:)
temp_SST_2CO2 = f_2CO2->TS(30:59,:,:)
temp_SOM_UNIF = e_UNIF_22_5->TS(40:99,:,:)
temp_SST_UNIF = f_UNIF_22_5->TS(30:59,:,:)

; Calculate differences with respect to 2CO2 but with reversed sign (1CO2-2CO2, UNIF-2CO2)
temp_diff_SOM_1CO2_2CO2 = temp_SOM_1CO2 - temp_SOM_2CO2  ; 1CO2 - 2CO2
temp_diff_SST_1CO2_2CO2 = temp_SST_1CO2 - temp_SST_2CO2  ; 1CO2 - 2CO2
temp_diff_SOM_UNIF_2CO2 = temp_SOM_UNIF - temp_SOM_2CO2  ; UNIF - 2CO2
temp_diff_SST_UNIF_2CO2 = temp_SST_UNIF - temp_SST_2CO2  ; UNIF - 2CO2

; Calculate zonal means (average over time and longitude)
DELTA_T_SOM_1CO2_2CO2_zonal = dim_avg_n(dim_avg_n(temp_diff_SOM_1CO2_2CO2, 0), 1)  ; average over time and longitude
DELTA_T_SST_1CO2_2CO2_zonal = dim_avg_n(dim_avg_n(temp_diff_SST_1CO2_2CO2, 0), 1)  ; average over time and longitude
DELTA_T_SOM_UNIF_2CO2_zonal = dim_avg_n(dim_avg_n(temp_diff_SOM_UNIF_2CO2, 0), 1)  ; average over time and longitude
DELTA_T_SST_UNIF_2CO2_zonal = dim_avg_n(dim_avg_n(temp_diff_SST_UNIF_2CO2, 0), 1)  ; average over time and longitude

; Calculate zonal means of NSST for radiative forcing calculation
DELTA_N_SST_1CO2_2CO2_zonal = dim_avg_n(dim_avg_n(NET_RAD_SST_1CO2 - NET_RAD_SST_2CO2, 0), 1)  ; average over time and longitude
DELTA_N_SST_UNIF_2CO2_zonal = dim_avg_n(dim_avg_n(NET_RAD_SST_UNIF_22_5 - NET_RAD_SST_2CO2, 0), 1)  ; average over time and longitude

; Then calculate slow response (SOM-SST) by latitude
DELTA_T_SLOW_1CO2_2CO2_zonal = DELTA_T_SOM_1CO2_2CO2_zonal - DELTA_T_SST_1CO2_2CO2_zonal  ; 1CO2 - 2CO2
DELTA_T_SLOW_UNIF_2CO2_zonal = DELTA_T_SOM_UNIF_2CO2_zonal - DELTA_T_SST_UNIF_2CO2_zonal  ; UNIF - 2CO2

; Calculate RF by latitude using the formula F = ΔNSST + λ ΔTSST
RF_1CO2_2CO2_zonal = DELTA_N_SST_1CO2_2CO2_zonal + (Lamda_1CO2_2CO2 * DELTA_T_SST_1CO2_2CO2_zonal)  ; 1CO2 - 2CO2
RF_UNIF_2CO2_zonal = DELTA_N_SST_UNIF_2CO2_zonal + (Lamda_UNIF_2CO2 * DELTA_T_SST_UNIF_2CO2_zonal)  ; UNIF - 2CO2

; Copy coordinate metadata
copy_VarCoords(lat, RF_1CO2_2CO2_zonal)
copy_VarCoords(lat, RF_UNIF_2CO2_zonal)
copy_VarCoords(lat, DELTA_T_SLOW_1CO2_2CO2_zonal)
copy_VarCoords(lat, DELTA_T_SLOW_UNIF_2CO2_zonal)
copy_VarCoords(lat, DELTA_N_SST_1CO2_2CO2_zonal)
copy_VarCoords(lat, DELTA_N_SST_UNIF_2CO2_zonal)
copy_VarCoords(lat, DELTA_T_SST_1CO2_2CO2_zonal)
copy_VarCoords(lat, DELTA_T_SST_UNIF_2CO2_zonal)


; Print information about the calculations
print("Radiative Forcing calculation (F = ΔNSST + λ ΔTSST):")
print(ERF)
print("")
print("Alternative Radiative Forcing calculation (F = λ × (TSOM - TSST)):")
print(RF_SLOW)
print("")
print("Lambda values:")
print("Lambda_1CO2_2CO2 = " + Lamda_1CO2_2CO2)
print("Lambda_UNIF_2CO2 = " + Lamda_UNIF_2CO2)
print("")
print("Slow response temperature changes (SOM-SST):")
print("DELTA_T_SLOW_1CO2_2CO2 = " + DELTA_T_SLOW_1CO2_2CO2)
print("DELTA_T_SLOW_UNIF_2CO2 = " + DELTA_T_SLOW_UNIF_2CO2)
print("")
print("SST temperature differences:")
print("DELTA_T_SST_1CO2_2CO2 = " + dim_avg_n_Wrap(DELTA_T_SST_1CO2_2CO2, 0))
print("DELTA_T_SST_UNIF_2CO2 = " + dim_avg_n_Wrap(DELTA_T_SST_UNIF_2CO2, 0))
print("DELTA_N_SOM_UNIF_2CO2_GLOBALmean = ")
print((dim_avg_n_Wrap(DELTA_N_SOM_UNIF_2CO2_GLOBALmean, 0)))

; Save radiative forcing results
asciiwrite("RF_globalmean_1CO2-2CO2_UNIF-2CO2.txt",ERF)

; Save alternative radiative forcing results
asciiwrite("RF_SLOW_globalmean_1CO2-2CO2_UNIF-2CO2.txt",RF_SLOW)

;----------------------------------------------------------------------
; Create plot of normalized RF vs latitude
;----------------------------------------------------------------------

; Normalize the RF by the slow change in global mean surface temperature
RF_1CO2_2CO2_zonal_normalized = RF_1CO2_2CO2_zonal / abs(DELTA_T_SLOW_1CO2_2CO2)
RF_UNIF_2CO2_zonal_normalized = RF_UNIF_2CO2_zonal / abs(DELTA_T_SLOW_UNIF_2CO2)

; Copy coordinate metadata
copy_VarCoords(lat, RF_1CO2_2CO2_zonal_normalized)
copy_VarCoords(lat, RF_UNIF_2CO2_zonal_normalized)

; Set up the output workstation
wks = gsn_open_wks("png","RF_normalized_vs_latitude_1CO2-2CO2_UNIF-2CO2")

; Set up resources for the plot
res                   = True
res@gsnDraw           = False             ; Don't draw plot yet
res@gsnFrame          = False             ; Don't advance frame yet
res@gsnMaximize       = True              ; Maximize plot in frame

; Add a legend
res@pmLegendDisplayMode    = "Always"            ; Turn on legend
res@pmLegendSide           = "Top"               ; Change location of
res@pmLegendParallelPosF   = 0.85                ; Move legend to right
res@pmLegendOrthogonalPosF = -0.35               ; Move legend down
res@pmLegendWidthF         = 0.15                ; Change width and
res@pmLegendHeightF        = 0.15                ; height of legend
res@lgLabelFontHeightF     = 0.015
res@lgPerimOn              = False               ; Turn off legend perimeter

; X-axis labels
res@tiXAxisString        = "Latitude"           ; X-axis label
res@tiXAxisFontHeightF   = 0.02                 ; X-axis font size

; Y-axis labels
res@tiYAxisString        = "Normalized Radiative Forcing (W/m~S~2~N~/K)"  ; Y-axis label
res@tiYAxisFontHeightF   = 0.02                 ; Y-axis font size

; Main title
res@tiMainString         = "Normalized Radiative Forcing vs Latitude (1CO2-2CO2, UNIF-2CO2)"  ; Main title
res@tiMainFontHeightF    = 0.025                ; Main title font size

; Line styles and colors
res@xyLineThicknessF     = 3.0                  ; Line thickness
res@xyLineColors         = (/"red", "blue"/)     ; Line colors
res@xyDashPatterns       = (/0, 0/)             ; Line dash patterns (0=solid)
res@xyExplicitLegendLabels = (/"1CO2-2CO2", "UNIF-2CO2"/) ; Legend labels

; Print dimensions to debug the plotting error
print("lat dimensions: ")
printVarSummary(lat)
print("RF_1CO2_2CO2_zonal_normalized dimensions: ")
printVarSummary(RF_1CO2_2CO2_zonal_normalized)
print("RF_UNIF_2CO2_zonal_normalized dimensions: ")
printVarSummary(RF_UNIF_2CO2_zonal_normalized)

; Create a multi-dimensional array for plotting
plotData = new((/2, dimsizes(lat)/), float)
plotData(0,:) = RF_1CO2_2CO2_zonal_normalized  ; 1CO2 - 2CO2
plotData(1,:) = RF_UNIF_2CO2_zonal_normalized  ; UNIF - 2CO2

; Create the plot
plot = gsn_csm_xy(wks, lat, plotData, res)

; Draw the plot
draw(plot)
frame(wks)

; Save the normalized zonal RF data to a file
zonal_rf_normalized_data = new((/2, dimsizes(lat)/), float)
zonal_rf_normalized_data(0,:) = RF_1CO2_2CO2_zonal_normalized  ; 1CO2 - 2CO2
zonal_rf_normalized_data(1,:) = RF_UNIF_2CO2_zonal_normalized  ; UNIF - 2CO2
asciiwrite("RF_normalized_zonal_1CO2-2CO2_UNIF-2CO2.txt", zonal_rf_normalized_data)

exit()

end
