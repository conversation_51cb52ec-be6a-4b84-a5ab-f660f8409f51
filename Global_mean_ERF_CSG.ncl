load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************



; e_path="/Volumes/Backup Plus"
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle" 
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")

e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
;e_TROP_22_5 = addfile(e_path+"/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_TROP_01_100_2D_1.nc", "r")





f_1CO2 = addfile(e_path+"/Yearly_F_CO2_01_60_new1.nc", "r")
f_2CO2 = addfile(e_path+"/Yearly_F_2CO2_01_60_new1.nc", "r")

f_UNIF_22_5 = addfile(e_path+"/Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc", "r")
;f_TROP_22_5 = addfile(e_path+"/Data/Yearmean_data/Prescribed_SST/22_5/Yearly_F2000_2CO2_TROP_01_60_2D_1.nc", "r")



NET_RAD_SST_1CO2          = (/f_1CO2->FSNT(30:59,:,:) - f_1CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_2CO2          = (/f_2CO2->FSNT(30:59,:,:) - f_2CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_UNIF_22_5          = (/f_UNIF_22_5->FSNT(30:59,:,:) - f_UNIF_22_5->FLNT(30:59,:,:)/)
;NET_RAD_SST_TROP_22_5          = (/f_TROP_22_5->FSNT(30:59,:,:) - f_TROP_22_5->FLNT(30:59,:,:)/)


NET_RAD_SOM_1CO2          = (/e_1CO2->FSNT(40:99,:,:) - e_1CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_2CO2          = (/e_2CO2->FSNT(40:99,:,:) - e_2CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_UNIF_22_5          = (/e_UNIF_22_5->FSNT(40:99,:,:) - e_UNIF_22_5->FLNT(40:99,:,:)/)
;NET_RAD_SOM_TROP_22_5          = (/e_TROP_22_5->FSNT(40:99,:,:) - e_TROP_22_5->FLNT(40:99,:,:)/)

;---------------------------------------------------------
dummy1=f_1CO2->TS(:,:,:)
dummy2=e_1CO2->TS(:,:,:)

copy_VarCoords(dummy1,NET_RAD_SST_1CO2)
copy_VarCoords(dummy1,NET_RAD_SST_2CO2)
copy_VarCoords(dummy1,NET_RAD_SST_UNIF_22_5)
;copy_VarCoords(dummy1,NET_RAD_SST_TROP_22_5)



copy_VarCoords(dummy2,NET_RAD_SOM_1CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_2CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_UNIF_22_5)
;copy_VarCoords(dummy2,NET_RAD_SOM_TROP_22_5)

;******************************************                 Areal average
lat=f_1CO2->lat
lon=f_1CO2->lon

  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************   
dummy3=f_1CO2->lat
copy_VarCoords(dummy3,area)
print(area)


DELTA_N_SST_1CO2 = (wgt_areaave_Wrap(NET_RAD_SST_1CO2(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_UNIF_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
;DELTA_N_SST_TROP_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_TROP_22_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))

DELTA_T_SST_1CO2 = (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_UNIF_22_5 = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
;DELTA_T_SST_TROP_22_5 = (wgt_areaave_Wrap(f_TROP_22_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))

DELTA_N_SST_1CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
;DELTA_N_SST_TROP_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_TROP_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))

DELTA_T_SST_1CO2_GLOBALmean = (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
;DELTA_T_SST_TROP_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_TROP_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))

DELTA_N_SOM_1CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
;DELTA_N_SOM_TROP_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_TROP_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))

DELTA_T_SOM_1CO2_GLOBALmean = (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
;DELTA_T_SOM_TROP_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_TROP_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))

; Lamda_1CO2 = (dim_avg_n_Wrap(DELTA_N_SST_1CO2_GLOBALmean(30:59), 0) - dim_avg_n_Wrap(DELTA_N_SOM_1CO2_GLOBALmean(40:99), 0)) / (dim_avg_n_Wrap(DELTA_T_SST_1CO2_GLOBALmean(30:59), 0) - dim_avg_n_Wrap(DELTA_T_SOM_1CO2_GLOBALmean(40:99), 0))
; ERF_1CO2 = dim_avg_n_Wrap((DELTA_N_SST_1CO2(30:59) - (Lamda_1CO2 * DELTA_T_SST_1CO2(30:59))),0)
printVarSummary(DELTA_T_SST_1CO2)
Lamda_1CO2 = (dim_avg_n_Wrap(DELTA_N_SST_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_1CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SOM_1CO2_GLOBALmean, 0))
Lamda_UNIF_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0))
;Lamda_TROP_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_TROP_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_TROP_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_TROP_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_TROP_GLOBALmean_22_5, 0))


ERF_1CO2 = dim_avg_n_Wrap((DELTA_N_SST_1CO2 - (Lamda_1CO2 * DELTA_T_SST_1CO2)),0)
ERF_UNIF_22_5 = dim_avg_n_Wrap((DELTA_N_SST_UNIF_22_5 - (Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5)),0)
;ERF_TROP_22_5 = dim_avg_n_Wrap((DELTA_N_SST_TROP_22_5 - (Lamda_TROP_22_5 * DELTA_T_SST_TROP_22_5)),0)

ERF=new((/2/),typeof(dummy2),dummy2@_FillValue)
ERF(0) = dim_avg_n_Wrap((DELTA_N_SST_1CO2 - (Lamda_1CO2 * DELTA_T_SST_1CO2)),0)
ERF(1) = dim_avg_n_Wrap((DELTA_N_SST_UNIF_22_5 - (Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5)),0)
;ERF(2) = dim_avg_n_Wrap((DELTA_N_SST_TROP_22_5 - (Lamda_TROP_22_5 * DELTA_T_SST_TROP_22_5)),0)


; ERF_1CO2_1 = dim_avg_n_Wrap((DELTA_N_SST_1CO2(30:59) - (Lamda_1CO2 * DELTA_T_SST_1CO2(30:59))),0)

;  print(Label_Global_mean)
;------------------------------------------------ 
; asciiwrite("Change_AOD_mean_rectangular_grid.txt",Label_Global_mean)   
; asciiwrite("Change_AOD_mean_rectangular_grid_2point.txt",Label_Global_mean1)   
; asciiwrite("Change_AOD_mean_rectangular_grid_all.txt",Global_mean)   
; print(Lamda_UNIF_22_5)
print(ERF)
print(DELTA_T_SST_1CO2 )
; print((dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_15, 0)))
print((dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0)))

; asciiwrite("ERF_globalmean_rectangular_grid_all.txt",ERF)   
;asciiwrite("ERF_globalmean_rectangular_grid_all_new_2lev.txt",ERF)   

  exit()
  
end
  
  
 
