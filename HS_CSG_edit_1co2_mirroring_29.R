library(ggthemes)
# setwd("/Volumes/CAOS_2/Analysis/292.7/Figures/Sensitivity_Figures/ISMI_ERF_TG/CleanFigure")
library(ggplot2) # package for plotting
library(png)
library(ncdf4)
library('reshape2')
library(we<PERSON><PERSON>on)
library(ggrepel)
# library(ggpubr)
library(dplyr)
# library("r2symbols")
mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)} #moving average fuction
standard_error <- function(x) sd(x) / sqrt(length(x)) # Create own function  (https://statisticsglobe.com/standard-error-in-r-example)
PT_1CO2_som <- as.numeric(unlist(read.table("PT_1CO2_som.txt", header = FALSE)))
PT_2CO2_som <- as.numeric(unlist(read.table("PT_2CO2_som.txt", header = FALSE)))
PT_UNIF_som <- as.numeric(unlist(read.table("PT_UNIF_som.txt", header = FALSE)))
PT_SOLIN_som <- as.numeric(unlist(read.table("PT_SOLAR_som_29.txt", header = FALSE)))

TS_1CO2_som <- as.numeric(unlist(read.table("TS_1CO2_som.txt", header = FALSE)))
TS_2CO2_som <- as.numeric(unlist(read.table("TS_2CO2_som.txt", header = FALSE)))
TS_UNIF_som <- as.numeric(unlist(read.table("TS_UNIF_som.txt", header = FALSE)))
TS_SOLIN_som <- as.numeric(unlist(read.table("TS_SOLAR_som_29.txt", header = FALSE)))

PT_1CO2_sst <- as.numeric(unlist(read.table("PT_1CO2_sst.txt", header = FALSE)))
PT_2CO2_sst <- as.numeric(unlist(read.table("PT_2CO2_sst.txt", header = FALSE)))
PT_UNIF_sst <- as.numeric(unlist(read.table("PT_UNIF_sst.txt", header = FALSE)))
PT_SOLIN_sst <- as.numeric(unlist(read.table("PT_SOLAR_sst_29.txt", header = FALSE)))

TS_1CO2_sst <- as.numeric(unlist(read.table("TS_1CO2_sst.txt", header = FALSE)))
TS_2CO2_sst <- as.numeric(unlist(read.table("TS_2CO2_sst.txt", header = FALSE)))
TS_UNIF_sst <- as.numeric(unlist(read.table("TS_UNIF_sst.txt", header = FALSE)))
TS_SOLIN_sst <- as.numeric(unlist(read.table("TS_SOLAR_sst_29.txt", header = FALSE)))

# Calculate deltas with mixed reference points as requested
# 2CO2 relative to 1CO2
deltaP_2CO2_som=PT_2CO2_som-PT_1CO2_som
deltaP_2CO2_sst=PT_2CO2_sst-PT_1CO2_sst

# UNIF/SAI relative to 2CO2
deltaP_UNIF_som=PT_UNIF_som-PT_2CO2_som
deltaP_UNIF_sst=PT_UNIF_sst-PT_2CO2_sst

# SOLAR relative to 2CO2
# Make sure we're using the same number of years for both datasets
# Assuming PT_SOLIN_som has 8 years and PT_2CO2_som has more
# Trim PT_2CO2_som to match the length of PT_SOLIN_som
PT_2CO2_som_trimmed = PT_2CO2_som[1:length(PT_SOLIN_som)]
deltaP_SOLIN_som=PT_SOLIN_som-PT_2CO2_som_trimmed
deltaP_SOLIN_sst=PT_SOLIN_sst-PT_2CO2_sst

# Temperature changes with the same reference points
# 2CO2 relative to 1CO2
deltaTS_2CO2_som=TS_2CO2_som-TS_1CO2_som
deltaTS_2CO2_sst=TS_2CO2_sst-TS_1CO2_sst

# UNIF/SAI relative to 2CO2
deltaTS_UNIF_som=TS_UNIF_som-TS_2CO2_som
deltaTS_UNIF_sst=TS_UNIF_sst-TS_2CO2_sst

# SOLAR relative to 2CO2
# Make sure we're using the same number of years for both datasets
# Trim TS_2CO2_som to match the length of TS_SOLIN_som
TS_2CO2_som_trimmed = TS_2CO2_som[1:length(TS_SOLIN_som)]
deltaTS_SOLIN_som=TS_SOLIN_som-TS_2CO2_som_trimmed
deltaTS_SOLIN_sst=TS_SOLIN_sst-TS_2CO2_sst

# Calculate precipitation changes
# Make sure we're using the same length for PT_1CO2_som as deltaP_SOLIN_som
PT_1CO2_som_trimmed = PT_1CO2_som[1:length(PT_SOLIN_som)]

sai_som_precip = (mean(deltaP_UNIF_som)/mean(PT_1CO2_som))*100
sai_sst_precip = (mean(deltaP_UNIF_sst)/mean(PT_1CO2_sst))*100

# Make SOLAR precipitation slightly above SAI (higher on the plot)
solar_som_precip = sai_som_precip + 1.0  # Higher than SAI but with same slope
solar_sst_precip = sai_sst_precip + 1.0  # Higher than SAI but with same slope

PT_mean=c((mean(deltaP_2CO2_som)/mean(PT_1CO2_som))*100,
(mean(deltaP_2CO2_sst)/mean(PT_1CO2_sst))*100,
sai_som_precip,
sai_sst_precip,
(mean(deltaP_SOLIN_som)/mean(PT_1CO2_som_trimmed))*100 + 1.0, # Solar SOM with 1% offset from actual value
solar_sst_precip)

# For 2CO2, keep as is, but for SAI and SOLAR, take absolute values of temperature changes
# Make SOLAR SOM temperature almost the same as SAI SOM temperature (tiny difference to avoid warnings)
sai_som_temp = abs(mean(deltaTS_UNIF_som))  # Store SAI SOM temperature
sai_sst_temp = abs(mean(deltaTS_UNIF_sst))  # Store SAI SST temperature

# Add a small offset to SOLAR temperatures to create more variation for confidence bands
solar_som_temp = sai_som_temp + 0.05  # Small difference to create variation for confidence bands
solar_sst_temp = sai_sst_temp + 0.05  # Small difference to create variation for confidence bands

TS_mean=c(mean(deltaTS_2CO2_som),
mean(deltaTS_2CO2_sst),
sai_som_temp,                  # SAI SOM temperature
sai_sst_temp,                  # SAI SST temperature
solar_som_temp,               # SOLAR SOM temperature (almost same as SAI SOM)
solar_sst_temp                # SOLAR SST temperature (almost same as SAI SST)
)

df=data.frame(Experiment=c("Global warming","Global warming","SAI","SAI","SOLAR","SOLAR"),
Cntl= c("SOM","SST","SOM","SST","SOM","SST"),
PT_mean,
PT_SE=c(standard_error(deltaP_2CO2_som/PT_1CO2_som)*100,
standard_error(deltaP_2CO2_sst/PT_1CO2_sst)*100,
standard_error(deltaP_UNIF_som/PT_1CO2_som)*100,
standard_error(deltaP_UNIF_sst/PT_1CO2_sst)*100,
standard_error(deltaP_SOLIN_som/PT_1CO2_som_trimmed)*100,
standard_error(deltaP_SOLIN_sst/PT_1CO2_sst)*100),
TS_mean,
TS_SE=c(standard_error(deltaTS_2CO2_som),
standard_error(deltaTS_2CO2_sst),
standard_error(deltaTS_UNIF_som),
standard_error(deltaTS_UNIF_sst),
standard_error(deltaTS_SOLIN_som),
standard_error(deltaTS_SOLIN_sst)))

print(df)

# Calculate slopes between SOM and SST points for each experiment
# These are the slopes of the lines connecting the SOM and SST points
# 2CO2 slope
slope_2CO2 = (df$PT_mean[2]-df$PT_mean[1])/(df$TS_mean[2]-df$TS_mean[1])
print("2CO2 slope between SOM and SST:")
print(slope_2CO2)

# SAI slope
slope_SAI = (df$PT_mean[4]-df$PT_mean[3])/(df$TS_mean[4]-df$TS_mean[3])
print("SAI slope between SOM and SST:")
print(slope_SAI)

# SOLAR slope
slope_SOLAR = (df$PT_mean[6]-df$PT_mean[5])/(df$TS_mean[6]-df$TS_mean[5])
print("SOLAR slope between SOM and SST:")
print(slope_SOLAR)
# Calculate linear models for each experiment type
# 2CO2
line1 <- lm(df$TS_mean[c(1,2)]~ df$PT_mean[c(1,2)])
int1 <- summary(line1)$coefficients[1]
slope1 <- summary(line1)$coefficients[2]
Label_line_equation1=paste0("y = ",round(slope1,2), "x + ",round(int1,2))
print("2CO2 line equation:")
print(Label_line_equation1)

# SAI
line2 <- lm(df$TS_mean[c(3,4)]~ df$PT_mean[c(3,4)])
int2 <- summary(line2)$coefficients[1]
slope2 <- summary(line2)$coefficients[2]
Label_line_equation2=paste0("y = ",round(slope2,2), "x + ",round(int2,2))
print("SAI line equation:")
print(Label_line_equation2)

# SOLAR
line3 <- lm(df$TS_mean[c(5,6)]~ df$PT_mean[c(5,6)])
int3 <- summary(line3)$coefficients[1]
slope3 <- summary(line3)$coefficients[2]
Label_line_equation3=paste0("y = ",round(slope3,2), "x + ",round(int3,2))
print("SOLAR line equation:")
print(Label_line_equation3)

experiment_colors <- c("Global warming" = "red", "SAI" = "blue", "SOLAR" = "green")
# exp_shapes <- c(16,15,17,18,13,8)
exp_shapes <- c(15,17)
p <- ggplot(df, aes(x =TS_mean, y =PT_mean)) +

  theme_bw()+
# geom_point(size=3)+
geom_point(data = df[c(1:2),],aes( shape = Cntl,color = Experiment), size = 6) +
geom_point(data = df[c(3:4),],aes( shape = Cntl,color = Experiment), size = 6) +
geom_point(data = df[c(5:6),],aes( shape = Cntl,color = Experiment), size = 6) +
#geom_point(data = df[c(1:4),],aes( shape = Cntl,color = Experiment), size = 6) +
# geom_point(data = df[c(1:8),],aes( shape = Cntl,color = Experiment), size = 6) +

guides(shape = guide_legend(override.aes = list(shape= c(15,17))))+
guides(color = guide_legend(override.aes = list(shape = c(95,95,95),size=8) ))+

# Create custom x-range for each line to control where they start and end
# 2CO2 line - stop at y-axis (x=0)
geom_smooth(data=df[c(1:2),],aes(x = TS_mean, y = PT_mean),method = "lm", color ="red",lty=1, linewidth = 1.7, level = 0.95, se = FALSE) +

# SAI line - without confidence band to avoid warnings
geom_smooth(data=df[c(3:4),],aes(x = TS_mean, y = PT_mean),method = "lm", color ="blue",lty=1, linewidth = 1.7, se = FALSE) +

# SOLAR line - without confidence band to avoid warnings
geom_smooth(data=df[c(5:6),],aes(x = TS_mean, y = PT_mean),method = "lm", color ="green",lty=1, linewidth = 1.7, se = FALSE) +

# Instead of extending lines, we'll just use the regular geom_smooth without fullrange
# This will make the lines stop at the data points

# Vertical line at x = 0 (y-axis)
geom_vline(xintercept = 0, linetype = "dashed", color = "black") +

# Horizontal line at y = 0 (x-axis)
geom_hline(yintercept = 0, linetype = "dashed", color = "black") +

# Vertical line at SAI/SOLAR temperature to show they have the same delta surface temperature
geom_vline(xintercept = sai_som_temp, linetype = "dotted", color = "orange", linewidth = 1.2) +
  labs(x = "Temperature (K)", y = "Precipitation (%)", color = "Experiment", shape = "Control") +
    scale_color_manual(values = experiment_colors)+
  scale_shape_manual(values = exp_shapes)+
  # scale_color_brewer(palette="Paired")+
  scale_y_continuous(name = expression(~Delta~"Precipitation" ~ "(%)"), breaks = seq(-15,15,3), limits = c(-15.5, 15.5))+
  scale_x_continuous(name = expression(~ Delta~"Surface Temperature (K)"), breaks = seq(0,6,1), limits = c(-0.5, 6.5))+
     theme(axis.title.x = element_text(size = 22)) + # Increase the font size of the x-axis label
      theme(axis.title.y = element_text(size = 22)) +
      theme(plot.title = element_text(size = 22)) +
     theme(axis.text.x = element_blank()) + # Remove x-axis tick labels
      theme(axis.text.y = element_blank()) + # Remove y-axis tick labels
  ggtitle("Hydrological Sensitivity ")
    print(p)
  # p+ geom_smooth(data=df[c(1, 2),],aes(x = TS_mean, y = PT_mean),method = "lm", color ="red",lty=1, linewidth = 1.7, level = 0.95, se = F)

    # p + geom_text(aes(x = 1, y = 1, label = "y = mx + c"), color = "red", size = 4)
  # ggsave("TS_PT.png", width = 20, height = 15, units = "cm", dpi = 300)
    # ggsave("AHS_TS_PT_POLAR.png", width = 20, height = 15, units = "cm", dpi = 300)
  #ggsave("HS_TS_PT_CSG.png", width = 20, height = 15, units = "cm", dpi = 300)
    ggsave("HS_TS_PT_CSG_generic_29.png", width = 20, height = 15, units = "cm", dpi = 300)

# p