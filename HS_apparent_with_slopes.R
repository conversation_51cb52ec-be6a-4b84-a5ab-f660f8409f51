library(ggthemes)
library(ggplot2) # package for plotting
library(png)
library(ncdf4)
library('reshape2')
library(we<PERSON><PERSON>on)
library(ggrepel)
library(dplyr)

mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)} #moving average fuction
standard_error <- function(x) sd(x) / sqrt(length(x)) # Create own function  (https://statisticsglobe.com/standard-error-in-r-example)

# Load data
PT_1CO2_som <- as.numeric(unlist(read.table("PT_1CO2_som.txt", header = FALSE)))
PT_2CO2_som <- as.numeric(unlist(read.table("PT_2CO2_som.txt", header = FALSE)))
PT_UNIF_som <- as.numeric(unlist(read.table("PT_UNIF_som.txt", header = FALSE)))
PT_SOLIN_som <- as.numeric(unlist(read.table("PT_SOLAR_som_29.txt", header = FALSE)))

TS_1CO2_som <- as.numeric(unlist(read.table("TS_1CO2_som.txt", header = FALSE)))
TS_2CO2_som <- as.numeric(unlist(read.table("TS_2CO2_som.txt", header = FALSE)))
TS_UNIF_som <- as.numeric(unlist(read.table("TS_UNIF_som.txt", header = FALSE)))
TS_SOLIN_som <- as.numeric(unlist(read.table("TS_SOLAR_som_29.txt", header = FALSE)))

# Calculate deltas with mixed reference points
# 2CO2 relative to 1CO2
deltaP_2CO2_som = PT_2CO2_som - PT_1CO2_som

# UNIF/SAI relative to 2CO2
deltaP_UNIF_som = PT_UNIF_som - PT_2CO2_som

# SOLAR relative to 2CO2
# Make sure we're using the same number of years for both datasets
PT_2CO2_som_trimmed = PT_2CO2_som[1:length(PT_SOLIN_som)]
deltaP_SOLIN_som = PT_SOLIN_som - PT_2CO2_som_trimmed

# Temperature changes with the same reference points
# 2CO2 relative to 1CO2
deltaTS_2CO2_som = TS_2CO2_som - TS_1CO2_som

# UNIF/SAI relative to 2CO2
deltaTS_UNIF_som = TS_UNIF_som - TS_2CO2_som

# SOLAR relative to 2CO2
TS_2CO2_som_trimmed = TS_2CO2_som[1:length(TS_SOLIN_som)]
deltaTS_SOLIN_som = TS_SOLIN_som - TS_2CO2_som_trimmed

# Calculate precipitation changes as percentages
# Original scenarios
sai_som_precip = (mean(deltaP_UNIF_som)/mean(PT_1CO2_som))*100
solar_som_precip = (mean(deltaP_SOLIN_som)/mean(PT_1CO2_som_trimmed))*100
gw_som_precip = (mean(deltaP_2CO2_som)/mean(PT_1CO2_som))*100

# Calculate combined scenarios with 1.2 * GW slope
# SAI + GW with 1.2 multiplier
sai_gw_ts = abs(mean(deltaTS_UNIF_som))
sai_gw_pt = 1.2 * HS_2CO2 * sai_gw_ts

# SOLAR + GW with 1.2 multiplier
solar_gw_ts = abs(mean(deltaTS_SOLIN_som))
solar_gw_pt = 1.2 * HS_2CO2 * solar_gw_ts

# Create data frame with SOM scenarios only
PT_mean = c(
  # Original scenarios
  gw_som_precip,
  sai_som_precip,
  solar_som_precip,
  # Combined scenarios
  sai_gw_pt,
  solar_gw_pt
)

# Temperature means - using absolute values
TS_mean = c(
  # Original scenarios
  abs(mean(deltaTS_2CO2_som)),
  abs(mean(deltaTS_UNIF_som)),
  abs(mean(deltaTS_SOLIN_som)),
  # Combined scenarios
  sai_gw_ts,
  solar_gw_ts
)

# Create data frame
df = data.frame(
  Experiment = c(
    "Global warming",
    "SAI",
    "SOLAR",
    "SAI+GW",
    "SOLAR+GW"
  ),
  PT_mean,
  TS_mean
)

print(df)

# Calculate apparent hydrological sensitivity (HS) for each experiment
# This is the ratio of precipitation change to temperature change
HS_2CO2 = df$PT_mean[1]/df$TS_mean[1]
print("Apparent HS for Global warming (2CO2):")
print(HS_2CO2)

HS_SAI = df$PT_mean[2]/df$TS_mean[2]
print("Apparent HS for SAI:")
print(HS_SAI)

HS_SOLAR = df$PT_mean[3]/df$TS_mean[3]
print("Apparent HS for SOLAR:")
print(HS_SOLAR)

# Define colors
experiment_colors <- c(
  "Global warming" = "red",
  "SAI" = "blue",
  "SOLAR" = "green",
  "SAI+GW" = "purple",
  "SOLAR+GW" = "orange"
)

# Create the plot
p <- ggplot() +
  theme_bw() +

  # Plot SOM points for each experiment
  geom_point(data = df, aes(x = TS_mean, y = PT_mean, color = Experiment), size = 6) +

  # Legend settings
  guides(color = guide_legend(override.aes = list(shape = c(16,16,16), size = 8))) +

  # Add lines from origin to SOM points to show apparent HS
  # 2CO2-1CO2
  geom_segment(aes(x = 0, y = 0, xend = df$TS_mean[1], yend = df$PT_mean[1]),
               color = "red", linewidth = 1.7, linetype = "solid") +
  # SAI-2CO2
  geom_segment(aes(x = 0, y = 0, xend = df$TS_mean[2], yend = df$PT_mean[2]),
               color = "blue", linewidth = 1.7, linetype = "solid") +
  # SOLAR-2CO2
  geom_segment(aes(x = 0, y = 0, xend = df$TS_mean[3], yend = df$PT_mean[3]),
               color = "green", linewidth = 1.7, linetype = "solid") +

  # Add lines for SAI+GW and SOLAR+GW points
  # SAI+GW
  geom_segment(aes(x = 0, y = 0,
                  xend = df$TS_mean[4],
                  yend = df$PT_mean[4]),
               color = "purple", linewidth = 1.7, linetype = "dotted") +
  # SOLAR+GW
  geom_segment(aes(x = 0, y = 0,
                  xend = df$TS_mean[5],
                  yend = df$PT_mean[5]),
               color = "orange", linewidth = 1.7, linetype = "dotted") +

  # Add reference lines
  geom_vline(xintercept = 0, linetype = "dashed", color = "black") +
  geom_hline(yintercept = 0, linetype = "dashed", color = "black") +


  # Labels and scales
  labs(x = "Temperature (K)", y = "Precipitation (%)", color = "Experiment") +
  scale_color_manual(values = experiment_colors) +
  scale_y_continuous(name = expression(~Delta~"Precipitation" ~ "(%)"), breaks = seq(-15, 15, 3), limits = c(-15.5, 15.5)) +
  scale_x_continuous(name = expression(~ "|"~Delta~"Surface Temperature|" ~ "(K)"), breaks = seq(0, 6, 1), limits = c(0, 6.5)) +

  # Theme settings
  theme(axis.title.x = element_text(size = 22)) +
  theme(axis.title.y = element_text(size = 22)) +
  theme(plot.title = element_text(size = 22)) +
  theme(axis.text.x = element_blank()) +
  theme(axis.text.y = element_blank()) +
  ggtitle("Apparent Hydrological Sensitivity with GW Slope")

print(p)

# Save the plot
ggsave("HS_apparent_with_slopes.png", width = 20, height = 15, units = "cm", dpi = 300)
