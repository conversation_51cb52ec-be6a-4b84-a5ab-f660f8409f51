load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle" 
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle" 
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/E2000_solin/Yearly_E2000_solin_01_100_1.nc", "r")

; Get latitude and longitude
lat = e_1CO2->lat
lon = e_1CO2->lon

; Calculate area weights for proper global averaging
rad = 4.0*atan(1.0)/180.0
clat = cos(lat*rad)

; Extract surface temperature data for all years
TS_SOM_1CO2 = e_1CO2->TS
TS_SOM_2CO2 = e_2CO2->TS
TS_SOM_UNIF = e_UNIF_22_5->TS
TS_SOM_SOLAR = e_SOLAR->TS

; Calculate global mean temperature for each year with proper area weighting
TS_SOM_1CO2_global = wgt_areaave_Wrap(TS_SOM_1CO2, clat, 1.0, 1)
TS_SOM_2CO2_global = wgt_areaave_Wrap(TS_SOM_2CO2, clat, 1.0, 1)
TS_SOM_UNIF_global = wgt_areaave_Wrap(TS_SOM_UNIF, clat, 1.0, 1)
TS_SOM_SOLAR_global = wgt_areaave_Wrap(TS_SOM_SOLAR, clat, 1.0, 1)

; Create time array (years)
nyears = dimsizes(TS_SOM_1CO2_global)
years = ispan(1, nyears, 1)

; Create a multi-dimensional array for temperature time series
tempTimeData = new((/4, nyears/), float)
tempTimeData(0,:) = TS_SOM_1CO2_global
tempTimeData(1,:) = TS_SOM_2CO2_global
tempTimeData(2,:) = TS_SOM_UNIF_global
tempTimeData(3,:) = TS_SOM_SOLAR_global

; Set up plot resources
wks = gsn_open_wks("pdf","TS_timeseries_SOM")

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"black","red","blue","green"/)
res@xyLineThicknesses = (/2.5, 2.5, 2.5, 2.5/)
res@xyDashPatterns    = (/0, 0, 0, 0/)         ; all solid lines
res@gsnXYTopLabel     = False
res@tiMainString      = "Global Mean Surface Temperature (SOM)"
res@tiYAxisString     = "Temperature (K)"
res@tiXAxisString     = "Year"

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"1xCO~B~2", "2xCO~B~2", "SAI", "SOLAR"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.15                       ;-- define legend width
res@pmLegendHeightF        =  0.15   
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

; Create the plot
plot = gsn_csm_xy(wks, years, tempTimeData, res)

exit()
  
end
  
  
 
