load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle" 
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle" 
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")

; Get latitude
lat = e_1CO2->lat

; Create arrays with the correct size
delta_TS_solar = new(dimsizes(lat), float)
adjusted_solar_ppt_delta = new(dimsizes(lat), float)

; Read the data from external files
asciiwrite("delta_TS_solar.txt", "-3.28914000000001, -3.25994, -3.31664000000004, -3.38644000000002, -3.38184000000004, -3.39914000000002, -3.36194000000004, -3.33854, -3.18003999999999, -3.33913999999996, -3.31824000000003, -3.30923999999996, -3.29144, -3.23553999999999, -3.41343999999995, -3.41814000000002, -3.25433999999999, -3.34774000000002, -3.43253999999999, -3.45714000000001, -3.37433999999999, -3.33143999999996, -3.41844000000001, -3.37384, -3.34493999999998, -3.33703999999998, -3.24364, -3.21794000000003, -3.22193999999999, -3.00023999999999, -3.05403999999996, -3.17823999999999, -3.17974000000001, -3.10724000000002, -3.08714000000001, -3.15394000000001, -3.08863999999997, -3.18593999999999, -3.17133999999996, -3.18593999999999, -3.20344000000003, -3.19713999999996, -3.20953999999998, -3.18764000000002, -3.07404, -3.18023999999994, -3.13073999999998, -3.04574, -3.04414, -3.16664, -3.03714, -3.11784000000003, -3.15883999999997, -3.11684, -2.94984000000003, -2.96253999999996, -3.15034, -3.14463999999996, -3.15774000000002, -3.32863999999998")

asciiwrite("adjusted_solar_ppt_delta.txt", "-0.249491833333333, -0.265245833333333, -0.233558833333333, -0.255190833333333, -0.246006833333333, -0.268446833333333, -0.242391833333333, -0.260340833333333, -0.247607833333333, -0.266291833333333, -0.248659833333333, -0.261449833333333, -0.254173833333333, -0.251265833333333, -0.260743833333333, -0.252447833333333, -0.247785833333333, -0.247324833333333, -0.280244833333333, -0.240713833333333, -0.269677833333333, -0.250922833333333, -0.264529833333333, -0.245145833333333, -0.255793833333333, -0.269532833333333, -0.251063833333333, -0.243772833333333, -0.275400833333333, -0.239116833333333, -0.239585833333333, -0.225571833333333, -0.245835833333333, -0.254823833333333, -0.218855833333333, -0.235055833333333, -0.239432833333333, -0.236526833333333, -0.258472833333333, -0.243706833333333, -0.236486833333333, -0.242756833333333, -0.230111833333333, -0.262637833333333, -0.230631833333333, -0.250447833333333, -0.254550833333333, -0.240319833333333, -0.218640833333333, -0.241964833333333, -0.252918833333333, -0.243337833333333, -0.248835833333333, -0.244588833333333, -0.231634833333333, -0.237038833333333, -0.244943833333333, -0.251356833333333, -0.246853833333333, -0.260600833333333")

; Manually assign values to the arrays
; For delta_TS_solar
delta_TS_solar(0) = -3.28914000000001
delta_TS_solar(1) = -3.25994
delta_TS_solar(2) = -3.31664000000004
delta_TS_solar(3) = -3.38644000000002
delta_TS_solar(4) = -3.38184000000004
delta_TS_solar(5) = -3.39914000000002
delta_TS_solar(6) = -3.36194000000004
delta_TS_solar(7) = -3.33854
delta_TS_solar(8) = -3.18003999999999
delta_TS_solar(9) = -3.33913999999996
delta_TS_solar(10) = -3.31824000000003
delta_TS_solar(11) = -3.30923999999996
delta_TS_solar(12) = -3.29144
delta_TS_solar(13) = -3.23553999999999
delta_TS_solar(14) = -3.41343999999995
delta_TS_solar(15) = -3.41814000000002
delta_TS_solar(16) = -3.25433999999999
delta_TS_solar(17) = -3.34774000000002
delta_TS_solar(18) = -3.43253999999999
delta_TS_solar(19) = -3.45714000000001
delta_TS_solar(20) = -3.37433999999999
delta_TS_solar(21) = -3.33143999999996
delta_TS_solar(22) = -3.41844000000001
delta_TS_solar(23) = -3.37384
delta_TS_solar(24) = -3.34493999999998
delta_TS_solar(25) = -3.33703999999998
delta_TS_solar(26) = -3.24364
delta_TS_solar(27) = -3.21794000000003
delta_TS_solar(28) = -3.22193999999999
delta_TS_solar(29) = -3.00023999999999
delta_TS_solar(30) = -3.05403999999996
delta_TS_solar(31) = -3.17823999999999
delta_TS_solar(32) = -3.17974000000001
delta_TS_solar(33) = -3.10724000000002
delta_TS_solar(34) = -3.08714000000001
delta_TS_solar(35) = -3.15394000000001
delta_TS_solar(36) = -3.08863999999997
delta_TS_solar(37) = -3.18593999999999
delta_TS_solar(38) = -3.17133999999996
delta_TS_solar(39) = -3.18593999999999
delta_TS_solar(40) = -3.20344000000003
delta_TS_solar(41) = -3.19713999999996
delta_TS_solar(42) = -3.20953999999998
delta_TS_solar(43) = -3.18764000000002
delta_TS_solar(44) = -3.07404
delta_TS_solar(45) = -3.18023999999994
delta_TS_solar(46) = -3.13073999999998
delta_TS_solar(47) = -3.04574
delta_TS_solar(48) = -3.04414
delta_TS_solar(49) = -3.16664
delta_TS_solar(50) = -3.03714
delta_TS_solar(51) = -3.11784000000003
delta_TS_solar(52) = -3.15883999999997
delta_TS_solar(53) = -3.11684
delta_TS_solar(54) = -2.94984000000003
delta_TS_solar(55) = -2.96253999999996
delta_TS_solar(56) = -3.15034
delta_TS_solar(57) = -3.14463999999996
delta_TS_solar(58) = -3.15774000000002
delta_TS_solar(59) = -3.32863999999998

; For adjusted_solar_ppt_delta
adjusted_solar_ppt_delta(0) = -0.249491833333333
adjusted_solar_ppt_delta(1) = -0.265245833333333
adjusted_solar_ppt_delta(2) = -0.233558833333333
adjusted_solar_ppt_delta(3) = -0.255190833333333
adjusted_solar_ppt_delta(4) = -0.246006833333333
adjusted_solar_ppt_delta(5) = -0.268446833333333
adjusted_solar_ppt_delta(6) = -0.242391833333333
adjusted_solar_ppt_delta(7) = -0.260340833333333
adjusted_solar_ppt_delta(8) = -0.247607833333333
adjusted_solar_ppt_delta(9) = -0.266291833333333
adjusted_solar_ppt_delta(10) = -0.248659833333333
adjusted_solar_ppt_delta(11) = -0.261449833333333
adjusted_solar_ppt_delta(12) = -0.254173833333333
adjusted_solar_ppt_delta(13) = -0.251265833333333
adjusted_solar_ppt_delta(14) = -0.260743833333333
adjusted_solar_ppt_delta(15) = -0.252447833333333
adjusted_solar_ppt_delta(16) = -0.247785833333333
adjusted_solar_ppt_delta(17) = -0.247324833333333
adjusted_solar_ppt_delta(18) = -0.280244833333333
adjusted_solar_ppt_delta(19) = -0.240713833333333
adjusted_solar_ppt_delta(20) = -0.269677833333333
adjusted_solar_ppt_delta(21) = -0.250922833333333
adjusted_solar_ppt_delta(22) = -0.264529833333333
adjusted_solar_ppt_delta(23) = -0.245145833333333
adjusted_solar_ppt_delta(24) = -0.255793833333333
adjusted_solar_ppt_delta(25) = -0.269532833333333
adjusted_solar_ppt_delta(26) = -0.251063833333333
adjusted_solar_ppt_delta(27) = -0.243772833333333
adjusted_solar_ppt_delta(28) = -0.275400833333333
adjusted_solar_ppt_delta(29) = -0.239116833333333
adjusted_solar_ppt_delta(30) = -0.239585833333333
adjusted_solar_ppt_delta(31) = -0.225571833333333
adjusted_solar_ppt_delta(32) = -0.245835833333333
adjusted_solar_ppt_delta(33) = -0.254823833333333
adjusted_solar_ppt_delta(34) = -0.218855833333333
adjusted_solar_ppt_delta(35) = -0.235055833333333
adjusted_solar_ppt_delta(36) = -0.239432833333333
adjusted_solar_ppt_delta(37) = -0.236526833333333
adjusted_solar_ppt_delta(38) = -0.258472833333333
adjusted_solar_ppt_delta(39) = -0.243706833333333
adjusted_solar_ppt_delta(40) = -0.236486833333333
adjusted_solar_ppt_delta(41) = -0.242756833333333
adjusted_solar_ppt_delta(42) = -0.230111833333333
adjusted_solar_ppt_delta(43) = -0.262637833333333
adjusted_solar_ppt_delta(44) = -0.230631833333333
adjusted_solar_ppt_delta(45) = -0.250447833333333
adjusted_solar_ppt_delta(46) = -0.254550833333333
adjusted_solar_ppt_delta(47) = -0.240319833333333
adjusted_solar_ppt_delta(48) = -0.218640833333333
adjusted_solar_ppt_delta(49) = -0.241964833333333
adjusted_solar_ppt_delta(50) = -0.252918833333333
adjusted_solar_ppt_delta(51) = -0.243337833333333
adjusted_solar_ppt_delta(52) = -0.248835833333333
adjusted_solar_ppt_delta(53) = -0.244588833333333
adjusted_solar_ppt_delta(54) = -0.231634833333333
adjusted_solar_ppt_delta(55) = -0.237038833333333
adjusted_solar_ppt_delta(56) = -0.244943833333333
adjusted_solar_ppt_delta(57) = -0.251356833333333
adjusted_solar_ppt_delta(58) = -0.246853833333333
adjusted_solar_ppt_delta(59) = -0.260600833333333

; Create a multi-dimensional array for plotting
plotData = new((/2, dimsizes(lat)/), float)
plotData(0,:) = delta_TS_solar
plotData(1,:) = adjusted_solar_ppt_delta * 10  ; Multiply by 10 to make it visible on the same scale

; Set up plot resources
wks = gsn_open_wks("pdf","Solar_data_vs_lat")

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"red","blue"/)
res@xyLineThicknesses = (/2.5, 2.5/)
res@xyDashPatterns    = (/0, 0/)         ; all solid lines
res@gsnXYTopLabel     = False
res@tiMainString      = "Solar Data vs Latitude"
res@tiYAxisString     = "Value"
res@tiXAxisString     = "Latitude (~F34~0~F~)"
res@trXMinF           = -90
res@trXMaxF           = 90

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"Delta TS Solar", "Adjusted Solar PPT Delta (x10)"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.25                       ;-- define legend width
res@pmLegendHeightF        =  0.15   
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

; Create the plot
plot = gsn_csm_xy(wks, lat, plotData, res)

; Create a second plot with separate y-axes for each variable
wks2 = gsn_open_wks("pdf","Solar_data_vs_lat_dual_axes")

; First plot for delta_TS_solar
res1 = True
res1@gsnDraw           = False  ; Don't draw yet
res1@gsnFrame          = False  ; Don't advance frame
res1@xyLineColor       = "red"
res1@xyLineThickness   = 2.5
res1@tiMainString      = "Solar Data vs Latitude (Dual Y-Axes)"
res1@tiYAxisString     = "Delta TS Solar (K)"
res1@tiXAxisString     = "Latitude (~F34~0~F~)"
res1@trXMinF           = -90
res1@trXMaxF           = 90
res1@trYMinF           = min(delta_TS_solar) - 0.1
res1@trYMaxF           = max(delta_TS_solar) + 0.1

res1@vpHeightF         = 0.6
res1@vpWidthF          = 0.75

plot1 = gsn_csm_xy(wks2, lat, delta_TS_solar, res1)

; Second plot for adjusted_solar_ppt_delta with different y-axis
res2 = True
res2@gsnDraw           = False  ; Don't draw yet
res2@gsnFrame          = False  ; Don't advance frame
res2@xyLineColor       = "blue"
res2@xyLineThickness   = 2.5
res2@tiYAxisString     = "Adjusted Solar PPT Delta"
res2@trXMinF           = -90
res2@trXMaxF           = 90
res2@trYMinF           = min(adjusted_solar_ppt_delta) - 0.01
res2@trYMaxF           = max(adjusted_solar_ppt_delta) + 0.01

; Use right y-axis for the second variable
res2@tmYROn            = True   ; Right y-axis labels on
res2@tmYRLabelsOn      = True
res2@tmYLOn            = False  ; Left y-axis labels off
res2@tmYLLabelsOn      = False
res2@tiYAxisSide       = "Right"

plot2 = gsn_csm_xy(wks2, lat, adjusted_solar_ppt_delta, res2)

; Overlay the plots
overlay(plot1, plot2)

; Add a legend
lgres = True
lgres@lgLineColors      = (/"red", "blue"/)
lgres@lgLineThicknesses = 2.5
lgres@lgDashIndexes     = (/0, 0/)
lgres@lgItemType        = "Lines"
lgres@lgLabelFontHeightF = 0.12
lgres@vpWidthF          = 0.15
lgres@vpHeightF         = 0.15
lgres@lgPerimOn         = True
lgres@lgPerimThicknessF = 1.0

labels = (/"Delta TS Solar", "Adjusted Solar PPT Delta"/)
legend = gsn_create_legend(wks2, 2, labels, lgres)

amres = True
amres@amParallelPosF   = 0.5
amres@amOrthogonalPosF = -0.5
annoid = gsn_add_annotation(plot1, legend, amres)

; Draw the plot
draw(plot1)
frame(wks2)

exit()
  
end
  
  
 
