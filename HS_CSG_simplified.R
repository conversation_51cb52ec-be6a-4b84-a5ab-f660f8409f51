library(ggthemes)
library(ggplot2) # package for plotting
library(png)
library(ncdf4)
library('reshape2')
library(we<PERSON><PERSON>on)
library(ggrepel)
library(dplyr)

mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)} #moving average fuction
standard_error <- function(x) sd(x) / sqrt(length(x)) # Create own function  (https://statisticsglobe.com/standard-error-in-r-example)

# Load data
PT_1CO2_som <- as.numeric(unlist(read.table("PT_1CO2_som.txt", header = FALSE)))
PT_2CO2_som <- as.numeric(unlist(read.table("PT_2CO2_som.txt", header = FALSE)))
PT_UNIF_som <- as.numeric(unlist(read.table("PT_UNIF_som.txt", header = FALSE)))
PT_SOLIN_som <- as.numeric(unlist(read.table("PT_SOLAR_som_29.txt", header = FALSE)))

TS_1CO2_som <- as.numeric(unlist(read.table("TS_1CO2_som.txt", header = FALSE)))
TS_2CO2_som <- as.numeric(unlist(read.table("TS_2CO2_som.txt", header = FALSE)))
TS_UNIF_som <- as.numeric(unlist(read.table("TS_UNIF_som.txt", header = FALSE)))
TS_SOLIN_som <- as.numeric(unlist(read.table("TS_SOLAR_som_29.txt", header = FALSE)))

PT_1CO2_sst <- as.numeric(unlist(read.table("PT_1CO2_sst.txt", header = FALSE)))
PT_2CO2_sst <- as.numeric(unlist(read.table("PT_2CO2_sst.txt", header = FALSE)))
PT_UNIF_sst <- as.numeric(unlist(read.table("PT_UNIF_sst.txt", header = FALSE)))
PT_SOLIN_sst <- as.numeric(unlist(read.table("PT_SOLAR_sst_29.txt", header = FALSE)))

TS_1CO2_sst <- as.numeric(unlist(read.table("TS_1CO2_sst.txt", header = FALSE)))
TS_2CO2_sst <- as.numeric(unlist(read.table("TS_2CO2_sst.txt", header = FALSE)))
TS_UNIF_sst <- as.numeric(unlist(read.table("TS_UNIF_sst.txt", header = FALSE)))
TS_SOLIN_sst <- as.numeric(unlist(read.table("TS_SOLAR_sst_29.txt", header = FALSE)))

# Calculate deltas with 1CO2 as the reference point for all scenarios
# 2CO2 relative to 1CO2
deltaP_2CO2_som = PT_2CO2_som - PT_1CO2_som
deltaP_2CO2_sst = PT_2CO2_sst - PT_1CO2_sst

# SAI/UNIF relative to 1CO2 (not 2CO2)
deltaP_UNIF_som = PT_UNIF_som - PT_1CO2_som
deltaP_UNIF_sst = PT_UNIF_sst - PT_1CO2_sst

# SOLAR relative to 1CO2 (not 2CO2)
# Make sure we're using the same number of years for both datasets
PT_1CO2_som_trimmed = PT_1CO2_som[1:length(PT_SOLIN_som)]
deltaP_SOLIN_som = PT_SOLIN_som - PT_1CO2_som_trimmed
deltaP_SOLIN_sst = PT_SOLIN_sst - PT_1CO2_sst

# Temperature changes with the same reference points
# 2CO2 relative to 1CO2
deltaTS_2CO2_som = TS_2CO2_som - TS_1CO2_som
deltaTS_2CO2_sst = TS_2CO2_sst - TS_1CO2_sst

# SAI/UNIF relative to 1CO2 (not 2CO2)
deltaTS_UNIF_som = TS_UNIF_som - TS_1CO2_som
deltaTS_UNIF_sst = TS_UNIF_sst - TS_1CO2_sst

# SOLAR relative to 1CO2 (not 2CO2)
TS_1CO2_som_trimmed = TS_1CO2_som[1:length(TS_SOLIN_som)]
deltaTS_SOLIN_som = TS_SOLIN_som - TS_1CO2_som_trimmed
deltaTS_SOLIN_sst = TS_SOLIN_sst - TS_1CO2_sst

# Calculate precipitation changes as percentages
# Original scenarios
sai_som_precip = (mean(deltaP_UNIF_som)/mean(PT_1CO2_som))*100
sai_sst_precip = (mean(deltaP_UNIF_sst)/mean(PT_1CO2_sst))*100

solar_som_precip = (mean(deltaP_SOLIN_som)/mean(PT_1CO2_som_trimmed))*100
solar_sst_precip = (mean(deltaP_SOLIN_sst)/mean(PT_1CO2_sst))*100

# Create data frame with all scenarios
PT_mean = c(
  # Original scenarios
  (mean(deltaP_2CO2_som)/mean(PT_1CO2_som))*100,
  (mean(deltaP_2CO2_sst)/mean(PT_1CO2_sst))*100,
  sai_som_precip,
  sai_sst_precip,
  solar_som_precip,
  solar_sst_precip
)

# Temperature means - using absolute values
TS_mean = c(
  # Original scenarios
  abs(mean(deltaTS_2CO2_som)),
  abs(mean(deltaTS_2CO2_sst)),
  abs(mean(deltaTS_UNIF_som)),
  abs(mean(deltaTS_UNIF_sst)),
  abs(mean(deltaTS_SOLIN_som)),
  abs(mean(deltaTS_SOLIN_sst))
)

# Create data frame
df = data.frame(
  Experiment = c(
    "2CO2-1CO2", "2CO2-1CO2", 
    "SAI-1CO2", "SAI-1CO2", 
    "SOLAR-1CO2", "SOLAR-1CO2"
  ),
  Cntl = c("SOM", "SST", "SOM", "SST", "SOM", "SST"),
  PT_mean,
  PT_SE = c(
    # Original scenarios
    standard_error(deltaP_2CO2_som/PT_1CO2_som)*100,
    standard_error(deltaP_2CO2_sst/PT_1CO2_sst)*100,
    standard_error(deltaP_UNIF_som/PT_1CO2_som)*100,
    standard_error(deltaP_UNIF_sst/PT_1CO2_sst)*100,
    standard_error(deltaP_SOLIN_som/PT_1CO2_som_trimmed)*100,
    standard_error(deltaP_SOLIN_sst/PT_1CO2_sst)*100
  ),
  TS_mean,
  TS_SE = c(
    # Original scenarios
    standard_error(abs(deltaTS_2CO2_som)),
    standard_error(abs(deltaTS_2CO2_sst)),
    standard_error(abs(deltaTS_UNIF_som)),
    standard_error(abs(deltaTS_UNIF_sst)),
    standard_error(abs(deltaTS_SOLIN_som)),
    standard_error(abs(deltaTS_SOLIN_sst))
  )
)

print(df)

# Calculate slopes between SOM and SST points for each experiment
# Original scenarios
slope_2CO2 = (df$PT_mean[2]-df$PT_mean[1])/(df$TS_mean[2]-df$TS_mean[1])
print("2CO2-1CO2 slope between SOM and SST:")
print(slope_2CO2)

slope_SAI = (df$PT_mean[4]-df$PT_mean[3])/(df$TS_mean[4]-df$TS_mean[3])
print("SAI-1CO2 slope between SOM and SST:")
print(slope_SAI)

slope_SOLAR = (df$PT_mean[6]-df$PT_mean[5])/(df$TS_mean[6]-df$TS_mean[5])
print("SOLAR-1CO2 slope between SOM and SST:")
print(slope_SOLAR)

# Calculate linear models for each experiment type
# 2CO2-1CO2
line1 <- lm(df$TS_mean[c(1,2)]~ df$PT_mean[c(1,2)])
int1 <- summary(line1)$coefficients[1]
slope1 <- summary(line1)$coefficients[2]
Label_line_equation1=paste0("y = ",round(slope1,2), "x + ",round(int1,2))
print("2CO2-1CO2 line equation:")
print(Label_line_equation1)

# SAI-1CO2
line2 <- lm(df$TS_mean[c(3,4)]~ df$PT_mean[c(3,4)])
int2 <- summary(line2)$coefficients[1]
slope2 <- summary(line2)$coefficients[2]
Label_line_equation2=paste0("y = ",round(slope2,2), "x + ",round(int2,2))
print("SAI-1CO2 line equation:")
print(Label_line_equation2)

# SOLAR-1CO2
line3 <- lm(df$TS_mean[c(5,6)]~ df$PT_mean[c(5,6)])
int3 <- summary(line3)$coefficients[1]
slope3 <- summary(line3)$coefficients[2]
Label_line_equation3=paste0("y = ",round(slope3,2), "x + ",round(int3,2))
print("SOLAR-1CO2 line equation:")
print(Label_line_equation3)

# Define colors and shapes
experiment_colors <- c(
  "2CO2-1CO2" = "red", 
  "SAI-1CO2" = "blue", 
  "SOLAR-1CO2" = "green"
)
exp_shapes <- c(15,17)

# Create the plot
p <- ggplot(df, aes(x = TS_mean, y = PT_mean)) +
  theme_bw() +
  
  # Plot points for each experiment
  geom_point(data = df[c(1:2),], aes(shape = Cntl, color = Experiment), size = 6) +
  geom_point(data = df[c(3:4),], aes(shape = Cntl, color = Experiment), size = 6) +
  geom_point(data = df[c(5:6),], aes(shape = Cntl, color = Experiment), size = 6) +
  
  # Legend settings
  guides(shape = guide_legend(override.aes = list(shape = c(15,17)))) +
  guides(color = guide_legend(override.aes = list(shape = c(95,95,95), size = 8))) +
  
  # Add regression lines
  # Original scenarios
  geom_smooth(data = df[c(1:2),], aes(x = TS_mean, y = PT_mean), method = "lm", color = "red", lty = 1, linewidth = 1.7, se = FALSE) +
  geom_smooth(data = df[c(3:4),], aes(x = TS_mean, y = PT_mean), method = "lm", color = "blue", lty = 1, linewidth = 1.7, se = FALSE) +
  geom_smooth(data = df[c(5:6),], aes(x = TS_mean, y = PT_mean), method = "lm", color = "green", lty = 1, linewidth = 1.7, se = FALSE) +
  
  # Add reference lines
  geom_vline(xintercept = 0, linetype = "dashed", color = "black") +
  geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
  
  # Labels and scales
  labs(x = "Temperature (K)", y = "Precipitation (%)", color = "Experiment", shape = "Control") +
  scale_color_manual(values = experiment_colors) +
  scale_shape_manual(values = exp_shapes) +
  scale_y_continuous(name = expression(~Delta~"Precipitation" ~ "(%)"), breaks = seq(-15, 15, 3), limits = c(-15.5, 15.5)) +
  scale_x_continuous(name = expression(~ "|"~Delta~"Surface Temperature|" ~ "(K)"), breaks = seq(0, 6, 1), limits = c(0, 6.5)) +
  
  # Theme settings
  theme(axis.title.x = element_text(size = 22)) +
  theme(axis.title.y = element_text(size = 22)) +
  theme(plot.title = element_text(size = 22)) +
  theme(axis.text.x = element_blank()) +
  theme(axis.text.y = element_blank()) +
  ggtitle("Hydrological Sensitivity (All Relative to 1CO2)")

print(p)

# Save the plot
ggsave("HS_TS_PT_simplified.png", width = 20, height = 15, units = "cm", dpi = 300)
