load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; e_path="/Volumes/Backup Plus"
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")

e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/29_4_solin/E2000_solin_29_4/Yearly_E2000_solin_29_4_01_48_1.nc", "r")




f_1CO2 = addfile(e_path+"/Yearly_F_CO2_01_60_new1.nc", "r")
f_2CO2 = addfile(e_path+"/Yearly_F_2CO2_01_60_new1.nc", "r")

f_UNIF_22_5 = addfile(e_path+"/Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc", "r")
f_SOLAR = addfile("/home/<USER>/Documents/Data_solin/29_4_solin/F2000_solin_29_4/Yearly_F2000_solin_29_4_01_60_1.nc", "r")



NET_RAD_SST_1CO2          = (/f_1CO2->FSNT(30:59,:,:) - f_1CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_2CO2          = (/f_2CO2->FSNT(30:59,:,:) - f_2CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_UNIF_22_5     = (/f_UNIF_22_5->FSNT(30:59,:,:) - f_UNIF_22_5->FLNT(30:59,:,:)/)
NET_RAD_SST_SOLAR         = (/f_SOLAR->FSNT(30:59,:,:) - f_SOLAR->FLNT(30:59,:,:)/)


NET_RAD_SOM_1CO2          = (/e_1CO2->FSNT(40:99,:,:) - e_1CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_2CO2          = (/e_2CO2->FSNT(40:99,:,:) - e_2CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_UNIF_22_5     = (/e_UNIF_22_5->FSNT(40:99,:,:) - e_UNIF_22_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_SOLAR         = (/e_SOLAR->FSNT(40:47,:,:) - e_SOLAR->FLNT(40:47,:,:)/)

;---------------------------------------------------------
dummy1=f_1CO2->TS(:,:,:)
dummy2=e_1CO2->TS(:,:,:)

copy_VarCoords(dummy1,NET_RAD_SST_1CO2)
copy_VarCoords(dummy1,NET_RAD_SST_2CO2)
copy_VarCoords(dummy1,NET_RAD_SST_UNIF_22_5)
copy_VarCoords(dummy1,NET_RAD_SST_SOLAR)



copy_VarCoords(dummy2,NET_RAD_SOM_1CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_2CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_UNIF_22_5)
copy_VarCoords(dummy2,NET_RAD_SOM_SOLAR)

;******************************************                 Areal average
lat=f_1CO2->lat
lon=f_1CO2->lon

  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************
dummy3=f_1CO2->lat
copy_VarCoords(dummy3,area)
;print(area)
dummy4=e_1CO2->TS(0,:,:)


; Average over time and longitude to get zonal mean forcing
; Calculate both 2CO2-1CO2 (warming) and 1CO2-2CO2 (cooling)
delta_N_SST_2CO2_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_2CO2, 2), 0) - dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_1CO2, 2), 0)
delta_N_SST_1CO2_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_1CO2, 2), 0) - dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_2CO2, 2), 0)

delta_N_SST_UNIF_22_5_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_UNIF_22_5, 2), 0) - dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_2CO2, 2), 0)

; Calculate delta_N_SST_SOLAR_lat similar to the other forcings
delta_N_SST_SOLAR_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_SOLAR, 2), 0) - dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_2CO2, 2), 0)

DELTA_T_SST_2CO2_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(f_2CO2->TS(30:59,:,:),2),0) - dim_avg_n_Wrap(dim_avg_n_Wrap(f_1CO2->TS(30:59,:,:),2),0)
DELTA_T_SST_1CO2_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(f_1CO2->TS(30:59,:,:),2),0) - dim_avg_n_Wrap(dim_avg_n_Wrap(f_2CO2->TS(30:59,:,:),2),0)
DELTA_T_SST_UNIF_22_5_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(f_UNIF_22_5->TS(30:59,:,:),2),0) - dim_avg_n_Wrap(dim_avg_n_Wrap(f_2CO2->TS(30:59,:,:),2),0)
DELTA_T_SST_SOLAR_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(f_SOLAR->TS(30:59,:,:),2),0) - dim_avg_n_Wrap(dim_avg_n_Wrap(f_2CO2->TS(30:59,:,:),2),0)


printVarSummary(DELTA_T_SST_2CO2_lat)
DELTA_N_SST_2CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1))
DELTA_N_SST_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_SOLAR_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_SOLAR,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))

DELTA_T_SST_2CO2_GLOBALmean = (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_1CO2_GLOBALmean = (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_SOLAR_GLOBALmean = (wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))

DELTA_N_SOM_2CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1))
DELTA_N_SOM_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_SOLAR_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_SOLAR,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2(0:7,:,:),area,1.0,1))

DELTA_T_SOM_2CO2_GLOBALmean = (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_SOLAR_GLOBALmean = (wgt_areaave_Wrap(e_SOLAR->TS(40:47,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:47,:,:),area,1.0,1))

; Calculate lambda for each forcing type
Lamda_2CO2 = (dim_avg_n_Wrap(DELTA_N_SST_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_2CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SOM_2CO2_GLOBALmean, 0))
Lamda_UNIF_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0))
Lamda_SOLAR = (dim_avg_n_Wrap(DELTA_N_SST_SOLAR_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_SOLAR_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_SOLAR_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SOM_SOLAR_GLOBALmean, 0))

; Calculate ERF for each latitude
deg2rad = 4.0 * atan(1.0) / 180.0
coslat  = cos(lat * deg2rad)

; For ERF_2CO2 (warming)
ERF_zonal_2CO2 = delta_N_SST_2CO2_lat - Lamda_2CO2 * DELTA_T_SST_2CO2_lat
ERF_2CO2 = dim_sum_n(ERF_zonal_2CO2 * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_1CO2 (cooling)
ERF_zonal_1CO2 = delta_N_SST_1CO2_lat - Lamda_2CO2 * DELTA_T_SST_1CO2_lat
ERF_1CO2 = dim_sum_n(ERF_zonal_1CO2 * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_UNIF_22_5
ERF_zonal_UNIF_22_5 = delta_N_SST_UNIF_22_5_lat - Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5_lat
ERF_UNIF_22_5 = dim_sum_n(ERF_zonal_UNIF_22_5 * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_SOLAR
ERF_zonal_SOLAR = delta_N_SST_SOLAR_lat - Lamda_SOLAR * DELTA_T_SST_SOLAR_lat
ERF_SOLAR = dim_sum_n(ERF_zonal_SOLAR * coslat, 0) / dim_sum_n(coslat, 0)

; Create a new array to hold all four ERF zonal profiles
ERF = new((/4, dimsizes(lat)/), typeof(dummy4), dummy4@_FillValue)
ERF(0,:) = ERF_zonal_2CO2
ERF(1,:) = ERF_zonal_UNIF_22_5
ERF(2,:) = ERF_zonal_SOLAR
ERF(3,:) = ERF_zonal_1CO2
ERF!0 = "case"
ERF!1 = "lat"
ERF&lat = lat

; Print the global mean ERF values
print("Global mean ERF values:")
print("2CO2 (warming): " + ERF_2CO2)
print("1CO2 (cooling): " + ERF_1CO2)
print("UNIF: " + ERF_UNIF_22_5)
print("SOLAR: " + ERF_SOLAR)
printVarSummary(ERF)
print(ERF)
;plotting
;***********************************************************
; Plot: Radiative Forcing vs Latitude
;***********************************************************

wks = gsn_open_wks("pdf","ERF_vs_lat_SCG_all_forcings_with_1CO2_29")   ; or "png" instead of "pdf"

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"red","blue","green","black"/)
res@xyLineThicknesses = (/2.5, 2.5, 2.5, 4.0/)
res@xyDashPatterns    = (/0, 0, 0, 16/)         ; solid for first 3, dashed for 1CO2
res@gsnXYTopLabel     = False
res@tiMainString      = "ERF vs Latitude"
res@tiYAxisString     = "ERF (Wm~S~-2~NN~)"
res@tiXAxisString     = "Latitude (~F34~0~F~)"
res@trXMinF           = -90
res@trXMaxF           = 90

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"2xCO~B~2 (warming)", "SAI", "SOLAR", "1xCO~B~2 (cooling)"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.82 ; fine-tune positioning
res@pmLegendWidthF         =  0.20                       ;-- define legend width
res@pmLegendHeightF        =  0.20
res@pmLegendOrthogonalPosF = -0.76
res@lgPerimOn = True           ; legend box border

plot = gsn_csm_xy(wks, lat, ERF, res)

exit()

end



