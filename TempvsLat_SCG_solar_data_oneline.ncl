load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle" 
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle" 
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")

; Get latitude
lat = e_1CO2->lat

; Define the delta TS solar values - all on one line
delta_TS_solar = (/-3.28914000000001, -3.25994, -3.31664000000004, -3.38644000000002, -3.38184000000004, -3.39914000000002, -3.36194000000004, -3.33854, -3.18003999999999, -3.33913999999996, -3.31824000000003, -3.30923999999996, -3.29144, -3.23553999999999, -3.41343999999995, -3.41814000000002, -3.25433999999999, -3.34774000000002, -3.43253999999999, -3.45714000000001, -3.37433999999999, -3.33143999999996, -3.41844000000001, -3.37384, -3.34493999999998, -3.33703999999998, -3.24364, -3.21794000000003, -3.22193999999999, -3.00023999999999, -3.05403999999996, -3.17823999999999, -3.17974000000001, -3.10724000000002, -3.08714000000001, -3.15394000000001, -3.08863999999997, -3.18593999999999, -3.17133999999996, -3.18593999999999, -3.20344000000003, -3.19713999999996, -3.20953999999998, -3.18764000000002, -3.07404, -3.18023999999994, -3.13073999999998, -3.04574, -3.04414, -3.16664, -3.03714, -3.11784000000003, -3.15883999999997, -3.11684, -2.94984000000003, -2.96253999999996, -3.15034, -3.14463999999996, -3.15774000000002, -3.32863999999998/)

; Define the adjusted solar_ppt delta values - all on one line
adjusted_solar_ppt_delta = (/-0.249491833333333, -0.265245833333333, -0.233558833333333, -0.255190833333333, -0.246006833333333, -0.268446833333333, -0.242391833333333, -0.260340833333333, -0.247607833333333, -0.266291833333333, -0.248659833333333, -0.261449833333333, -0.254173833333333, -0.251265833333333, -0.260743833333333, -0.252447833333333, -0.247785833333333, -0.247324833333333, -0.280244833333333, -0.240713833333333, -0.269677833333333, -0.250922833333333, -0.264529833333333, -0.245145833333333, -0.255793833333333, -0.269532833333333, -0.251063833333333, -0.243772833333333, -0.275400833333333, -0.239116833333333, -0.239585833333333, -0.225571833333333, -0.245835833333333, -0.254823833333333, -0.218855833333333, -0.235055833333333, -0.239432833333333, -0.236526833333333, -0.258472833333333, -0.243706833333333, -0.236486833333333, -0.242756833333333, -0.230111833333333, -0.262637833333333, -0.230631833333333, -0.250447833333333, -0.254550833333333, -0.240319833333333, -0.218640833333333, -0.241964833333333, -0.252918833333333, -0.243337833333333, -0.248835833333333, -0.244588833333333, -0.231634833333333, -0.237038833333333, -0.244943833333333, -0.251356833333333, -0.246853833333333, -0.260600833333333/)

; Check if the arrays have the same length as the latitude array
if (dimsizes(delta_TS_solar) .ne. dimsizes(lat)) then
  print("Warning: delta_TS_solar array length (" + dimsizes(delta_TS_solar) + ") does not match latitude array length (" + dimsizes(lat) + ")")
end if

if (dimsizes(adjusted_solar_ppt_delta) .ne. dimsizes(lat)) then
  print("Warning: adjusted_solar_ppt_delta array length (" + dimsizes(adjusted_solar_ppt_delta) + ") does not match latitude array length (" + dimsizes(lat) + ")")
end if

; Create a multi-dimensional array for plotting
plotData = new((/2, dimsizes(lat)/), float)
plotData(0,:) = delta_TS_solar
plotData(1,:) = adjusted_solar_ppt_delta * 10  ; Multiply by 10 to make it visible on the same scale

; Set up plot resources
wks = gsn_open_wks("pdf","Solar_data_vs_lat")

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"red","blue"/)
res@xyLineThicknesses = (/2.5, 2.5/)
res@xyDashPatterns    = (/0, 0/)         ; all solid lines
res@gsnXYTopLabel     = False
res@tiMainString      = "Solar Data vs Latitude"
res@tiYAxisString     = "Value"
res@tiXAxisString     = "Latitude (~F34~0~F~)"
res@trXMinF           = -90
res@trXMaxF           = 90

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"Delta TS Solar", "Adjusted Solar PPT Delta (x10)"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.25                       ;-- define legend width
res@pmLegendHeightF        =  0.15   
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

; Create the plot
plot = gsn_csm_xy(wks, lat, plotData, res)

; Create a second plot with separate y-axes for each variable
wks2 = gsn_open_wks("pdf","Solar_data_vs_lat_dual_axes")

; First plot for delta_TS_solar
res1 = True
res1@gsnDraw           = False  ; Don't draw yet
res1@gsnFrame          = False  ; Don't advance frame
res1@xyLineColor       = "red"
res1@xyLineThickness   = 2.5
res1@tiMainString      = "Solar Data vs Latitude (Dual Y-Axes)"
res1@tiYAxisString     = "Delta TS Solar (K)"
res1@tiXAxisString     = "Latitude (~F34~0~F~)"
res1@trXMinF           = -90
res1@trXMaxF           = 90
res1@trYMinF           = min(delta_TS_solar) - 0.1
res1@trYMaxF           = max(delta_TS_solar) + 0.1

res1@vpHeightF         = 0.6
res1@vpWidthF          = 0.75

plot1 = gsn_csm_xy(wks2, lat, delta_TS_solar, res1)

; Second plot for adjusted_solar_ppt_delta with different y-axis
res2 = True
res2@gsnDraw           = False  ; Don't draw yet
res2@gsnFrame          = False  ; Don't advance frame
res2@xyLineColor       = "blue"
res2@xyLineThickness   = 2.5
res2@tiYAxisString     = "Adjusted Solar PPT Delta"
res2@trXMinF           = -90
res2@trXMaxF           = 90
res2@trYMinF           = min(adjusted_solar_ppt_delta) - 0.01
res2@trYMaxF           = max(adjusted_solar_ppt_delta) + 0.01

; Use right y-axis for the second variable
res2@tmYROn            = True   ; Right y-axis labels on
res2@tmYRLabelsOn      = True
res2@tmYLOn            = False  ; Left y-axis labels off
res2@tmYLLabelsOn      = False
res2@tiYAxisSide       = "Right"

plot2 = gsn_csm_xy(wks2, lat, adjusted_solar_ppt_delta, res2)

; Overlay the plots
overlay(plot1, plot2)

; Add a legend
lgres = True
lgres@lgLineColors      = (/"red", "blue"/)
lgres@lgLineThicknesses = 2.5
lgres@lgDashIndexes     = (/0, 0/)
lgres@lgItemType        = "Lines"
lgres@lgLabelFontHeightF = 0.12
lgres@vpWidthF          = 0.15
lgres@vpHeightF         = 0.15
lgres@lgPerimOn         = True
lgres@lgPerimThicknessF = 1.0

labels = (/"Delta TS Solar", "Adjusted Solar PPT Delta"/)
legend = gsn_create_legend(wks2, 2, labels, lgres)

amres = True
amres@amParallelPosF   = 0.5
amres@amOrthogonalPosF = -0.5
annoid = gsn_add_annotation(plot1, legend, amres)

; Draw the plot
draw(plot1)
frame(wks2)

exit()
  
end
  
  
 
