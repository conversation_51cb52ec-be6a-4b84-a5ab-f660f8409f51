load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"

begin
;*************************************************

; Define paths and load data
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/29_4_solin/E2000_solin_29_4/Yearly_E2000_solin_29_4_01_48_1.nc", "r")

f_1CO2 = addfile(e_path+"/Yearly_F_CO2_01_60_new1.nc", "r")
f_2CO2 = addfile(e_path+"/Yearly_F_2CO2_01_60_new1.nc", "r")
f_UNIF_22_5 = addfile(e_path+"/Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc", "r")
f_SOLAR = addfile("/home/<USER>/Documents/Data_solin/29_4_solin/F2000_solin_29_4/Yearly_F2000_solin_29_4_01_60_1.nc", "r")

; Get latitude and longitude
lat = f_1CO2->lat
lon = f_1CO2->lon

; Create weights for area-weighted averaging
rad = 4.0*atan(1.0)/180.0
clat = cos(lat*rad)  ; cosine of latitude for area weighting

; Find indices for 20S and 20N
lat_south = -20.0  ; 20S
lat_north = 20.0   ; 20N

; Find the closest latitude indices
lat_s_idx = minind(abs(lat - lat_south))
lat_n_idx = minind(abs(lat - lat_north))

print("Latitude range indices: " + lat_s_idx + " to " + lat_n_idx)
print("Actual latitudes: " + lat(lat_s_idx) + " to " + lat(lat_n_idx))

; Extract the weights for the tropical band
tropical_weights = clat(lat_s_idx:lat_n_idx)

; Calculate tropical mean surface temperatures for SST runs
; Average over years 30-59 for SST runs
TS_SST_1CO2_tropical = wgt_areaave_Wrap(f_1CO2->TS(30:59,lat_s_idx:lat_n_idx,:), tropical_weights, 1.0, (/1,2/))
TS_SST_2CO2_tropical = wgt_areaave_Wrap(f_2CO2->TS(30:59,lat_s_idx:lat_n_idx,:), tropical_weights, 1.0, (/1,2/))
TS_SST_UNIF_tropical = wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,lat_s_idx:lat_n_idx,:), tropical_weights, 1.0, (/1,2/))
TS_SST_SOLAR_tropical = wgt_areaave_Wrap(f_SOLAR->TS(30:59,lat_s_idx:lat_n_idx,:), tropical_weights, 1.0, (/1,2/))

; Calculate tropical mean surface temperatures for SOM runs
; Average over years 40-99 for SOM runs (except SOLAR which uses 40-47)
TS_SOM_1CO2_tropical = wgt_areaave_Wrap(e_1CO2->TS(40:99,lat_s_idx:lat_n_idx,:), tropical_weights, 1.0, (/1,2/))
TS_SOM_2CO2_tropical = wgt_areaave_Wrap(e_2CO2->TS(40:99,lat_s_idx:lat_n_idx,:), tropical_weights, 1.0, (/1,2/))
TS_SOM_UNIF_tropical = wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,lat_s_idx:lat_n_idx,:), tropical_weights, 1.0, (/1,2/))
TS_SOM_SOLAR_tropical = wgt_areaave_Wrap(e_SOLAR->TS(40:47,lat_s_idx:lat_n_idx,:), tropical_weights, 1.0, (/1,2/))

; Calculate temperature differences for SST runs
DELTA_T_SST_2CO2_tropical = dim_avg_n_Wrap(TS_SST_2CO2_tropical, 0) - dim_avg_n_Wrap(TS_SST_1CO2_tropical, 0)
DELTA_T_SST_UNIF_tropical = dim_avg_n_Wrap(TS_SST_UNIF_tropical, 0) - dim_avg_n_Wrap(TS_SST_2CO2_tropical, 0)
DELTA_T_SST_SOLAR_tropical = dim_avg_n_Wrap(TS_SST_SOLAR_tropical, 0) - dim_avg_n_Wrap(TS_SST_2CO2_tropical, 0)
DELTA_T_SST_1CO2_tropical = dim_avg_n_Wrap(TS_SST_1CO2_tropical, 0) - dim_avg_n_Wrap(TS_SST_2CO2_tropical, 0)

; Calculate temperature differences for SOM runs
DELTA_T_SOM_2CO2_tropical = dim_avg_n_Wrap(TS_SOM_2CO2_tropical, 0) - dim_avg_n_Wrap(TS_SOM_1CO2_tropical, 0)
DELTA_T_SOM_UNIF_tropical = dim_avg_n_Wrap(TS_SOM_UNIF_tropical, 0) - dim_avg_n_Wrap(TS_SOM_2CO2_tropical, 0)
DELTA_T_SOM_SOLAR_tropical = dim_avg_n_Wrap(TS_SOM_SOLAR_tropical, 0) - dim_avg_n_Wrap(TS_SOM_2CO2_tropical(0:7), 0)
DELTA_T_SOM_1CO2_tropical = dim_avg_n_Wrap(TS_SOM_1CO2_tropical, 0) - dim_avg_n_Wrap(TS_SOM_2CO2_tropical, 0)

; Print results
print("=== Tropical (20S-20N) Surface Temperature Differences ===")
print("SST Runs (Fixed SST):")
print("2CO2 - 1CO2: " + DELTA_T_SST_2CO2_tropical + " K")
print("UNIF - 2CO2: " + DELTA_T_SST_UNIF_tropical + " K")
print("SOLAR - 2CO2: " + DELTA_T_SST_SOLAR_tropical + " K")
print("1CO2 - 2CO2: " + DELTA_T_SST_1CO2_tropical + " K")
print("")
print("SOM Runs (Coupled Ocean):")
print("2CO2 - 1CO2: " + DELTA_T_SOM_2CO2_tropical + " K")
print("UNIF - 2CO2: " + DELTA_T_SOM_UNIF_tropical + " K")
print("SOLAR - 2CO2: " + DELTA_T_SOM_SOLAR_tropical + " K")
print("1CO2 - 2CO2: " + DELTA_T_SOM_1CO2_tropical + " K")

; Calculate global mean surface temperatures for comparison
TS_SST_1CO2_global = wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:), clat, 1.0, (/1,2/))
TS_SST_2CO2_global = wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:), clat, 1.0, (/1,2/))
TS_SST_UNIF_global = wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:), clat, 1.0, (/1,2/))
TS_SST_SOLAR_global = wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:), clat, 1.0, (/1,2/))

TS_SOM_1CO2_global = wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:), clat, 1.0, (/1,2/))
TS_SOM_2CO2_global = wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:), clat, 1.0, (/1,2/))
TS_SOM_UNIF_global = wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:), clat, 1.0, (/1,2/))
TS_SOM_SOLAR_global = wgt_areaave_Wrap(e_SOLAR->TS(40:47,:,:), clat, 1.0, (/1,2/))

; Calculate global temperature differences
DELTA_T_SST_2CO2_global = dim_avg_n_Wrap(TS_SST_2CO2_global, 0) - dim_avg_n_Wrap(TS_SST_1CO2_global, 0)
DELTA_T_SST_UNIF_global = dim_avg_n_Wrap(TS_SST_UNIF_global, 0) - dim_avg_n_Wrap(TS_SST_2CO2_global, 0)
DELTA_T_SST_SOLAR_global = dim_avg_n_Wrap(TS_SST_SOLAR_global, 0) - dim_avg_n_Wrap(TS_SST_2CO2_global, 0)
DELTA_T_SST_1CO2_global = dim_avg_n_Wrap(TS_SST_1CO2_global, 0) - dim_avg_n_Wrap(TS_SST_2CO2_global, 0)

DELTA_T_SOM_2CO2_global = dim_avg_n_Wrap(TS_SOM_2CO2_global, 0) - dim_avg_n_Wrap(TS_SOM_1CO2_global, 0)
DELTA_T_SOM_UNIF_global = dim_avg_n_Wrap(TS_SOM_UNIF_global, 0) - dim_avg_n_Wrap(TS_SOM_2CO2_global, 0)
DELTA_T_SOM_SOLAR_global = dim_avg_n_Wrap(TS_SOM_SOLAR_global, 0) - dim_avg_n_Wrap(TS_SOM_2CO2_global(0:7), 0)
DELTA_T_SOM_1CO2_global = dim_avg_n_Wrap(TS_SOM_1CO2_global, 0) - dim_avg_n_Wrap(TS_SOM_2CO2_global, 0)

; Print global results for comparison
print("")
print("=== Global Surface Temperature Differences ===")
print("SST Runs (Fixed SST):")
print("2CO2 - 1CO2: " + DELTA_T_SST_2CO2_global + " K")
print("UNIF - 2CO2: " + DELTA_T_SST_UNIF_global + " K")
print("SOLAR - 2CO2: " + DELTA_T_SST_SOLAR_global + " K")
print("1CO2 - 2CO2: " + DELTA_T_SST_1CO2_global + " K")
print("")
print("SOM Runs (Coupled Ocean):")
print("2CO2 - 1CO2: " + DELTA_T_SOM_2CO2_global + " K")
print("UNIF - 2CO2: " + DELTA_T_SOM_UNIF_global + " K")
print("SOLAR - 2CO2: " + DELTA_T_SOM_SOLAR_global + " K")
print("1CO2 - 2CO2: " + DELTA_T_SOM_1CO2_global + " K")

; Calculate tropical to global temperature difference ratios
print("")
print("=== Tropical to Global Temperature Difference Ratios ===")
print("SST Runs (Fixed SST):")
print("2CO2 - 1CO2: " + DELTA_T_SST_2CO2_tropical/DELTA_T_SST_2CO2_global)
print("UNIF - 2CO2: " + DELTA_T_SST_UNIF_tropical/DELTA_T_SST_UNIF_global)
print("SOLAR - 2CO2: " + DELTA_T_SST_SOLAR_tropical/DELTA_T_SST_SOLAR_global)
print("1CO2 - 2CO2: " + DELTA_T_SST_1CO2_tropical/DELTA_T_SST_1CO2_global)
print("")
print("SOM Runs (Coupled Ocean):")
print("2CO2 - 1CO2: " + DELTA_T_SOM_2CO2_tropical/DELTA_T_SOM_2CO2_global)
print("UNIF - 2CO2: " + DELTA_T_SOM_UNIF_tropical/DELTA_T_SOM_UNIF_global)
print("SOLAR - 2CO2: " + DELTA_T_SOM_SOLAR_tropical/DELTA_T_SOM_SOLAR_global)
print("1CO2 - 2CO2: " + DELTA_T_SOM_1CO2_tropical/DELTA_T_SOM_1CO2_global)

exit()

end
