import matplotlib.pyplot as plt
import numpy as np

# Data for the graph
forcing_types = ['1CO2', 'Solar', 'UNIF']

# Temperature change values (in K) relative to 2CO2
# Negative values because we're doing 1CO2-2CO2, Solar-2CO2, UNIF-2CO2
temp_change = {
    # SOM (coupled ocean) values
    'SOM': {
        '1CO2': -3.0,  # 1CO2 is cooler than 2CO2
        'Solar': -3.0,  # Solar is cooler than 2CO2
        'UNIF': -3.0    # UNIF is cooler than 2CO2
    },
    # SST (fixed SST) values
    'SST': {
        '1CO2': 0.0,    # No temperature change in fixed SST
        'Solar': 0.0,    # No temperature change in fixed SST
        'UNIF': 0.0      # No temperature change in fixed SST
    }
}

# Precipitation change values (in percentage) relative to 2CO2
precip_change = {
    # SOM (coupled ocean) values
    'SOM': {
        '1CO2': -6.0,   # 1CO2 has less precipitation than 2CO2
        'Solar': -4.0,   # Solar has less precipitation than 2CO2
        'UNIF': -8.5     # UNIF has less precipitation than 2CO2
    },
    # SST (fixed SST) values - fast response only
    'SST': {
        '1CO2': 3.0,    # 1CO2 has more precipitation than 2CO2 in fixed SST
        'Solar': 0.0,    # Solar has ZERO fast response (no fast adjustments)
        'UNIF': 1.5      # UNIF has more precipitation than 2CO2 in fixed SST
    }
}

# Set up the figure and axis
fig, ax = plt.subplots(figsize=(10, 8))

# Colors for each forcing type
colors = {
    '1CO2': 'red',      # Red for CO2
    'Solar': 'green',    # Green for Solar
    'UNIF': 'blue'       # Blue for UNIF
}

# Plot precipitation vs temperature change
for forcing in forcing_types:
    # Plot SOM points (squares)
    ax.scatter(
        temp_change['SOM'][forcing],
        precip_change['SOM'][forcing],
        color=colors[forcing],
        marker='s',  # Square for SOM
        s=100,  # Size of marker
        label=f"{forcing} SOM"
    )

    # Plot SST points (triangles)
    ax.scatter(
        temp_change['SST'][forcing],
        precip_change['SST'][forcing],
        color=colors[forcing],
        marker='^',  # Triangle for SST
        s=100,  # Size of marker
        label=f"{forcing} SST"
    )

    # Draw lines connecting SOM and SST points
    ax.plot(
        [temp_change['SOM'][forcing], temp_change['SST'][forcing]],
        [precip_change['SOM'][forcing], precip_change['SST'][forcing]],
        color=colors[forcing],
        linestyle='-',
        linewidth=2
    )

# Add a horizontal line at y=0
ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)

# Add a vertical line at x=0
ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)

# Add labels and title
ax.set_xlabel('Δ Temperature (K)', fontsize=14)
ax.set_ylabel('Δ Precipitation (%)', fontsize=14)
ax.set_title('Hydrological sensitivity', fontsize=16)

# Add grid
ax.grid(True, linestyle='--', alpha=0.7)

# Add text box with additional information
#textstr = '\n'.join((
 #   'Forcing Type Characteristics:',
  #  'CO₂: Tropospheric heating, Hadley cell shift',
   # 'Solar: Minor circulation changes',
    #'SAI: Stratospheric heating, Strong tropical circulation weakening',
    #'',
    #'Note: The slope of each line represents the hydrological sensitivity.',
    #'The y-intercept (at x=0) represents the fast precipitation response.',
    #'The total response = fast response + slow response.'
#))
props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
#ax.text(0.05, 0.05, textstr, transform=ax.transAxes, fontsize=10,
 #       verticalalignment='bottom', bbox=props)

# Add a legend with custom handles
from matplotlib.lines import Line2D

# Create legend elements
legend_elements = []

# Control section (marker types)
legend_elements.append(Line2D([0], [0], marker='s', color='black', markerfacecolor='black',
                             markersize=8, linestyle='none', label='SOM'))
legend_elements.append(Line2D([0], [0], marker='^', color='black', markerfacecolor='black',
                             markersize=8, linestyle='none', label='SST'))

# Experiment section (colors)
legend_elements.append(Line2D([0], [0], marker='o', color='red', markerfacecolor='red',
                             markersize=8, linestyle='-', label='1CO2'))
legend_elements.append(Line2D([0], [0], marker='o', color='green', markerfacecolor='green',
                             markersize=8, linestyle='-', label='Solar'))
legend_elements.append(Line2D([0], [0], marker='o', color='blue', markerfacecolor='blue',
                             markersize=8, linestyle='-', label='SAI'))

# Create two-column legend
legend1 = ax.legend(handles=legend_elements[:2], loc='upper right', title='Control', fontsize=10, frameon=True)
legend2 = ax.legend(handles=legend_elements[2:], loc='lower right', title='Experiment', fontsize=10, frameon=True)

# Add the first legend back
ax.add_artist(legend1)

# Set axis limits to match the example plot
ax.set_xlim(-4.5, 0.5)
ax.set_ylim(-10, 4)

# Adjust layout and save the figure
plt.tight_layout()
plt.savefig('hydrological_sensitivity_plot.png', dpi=300)
plt.show()