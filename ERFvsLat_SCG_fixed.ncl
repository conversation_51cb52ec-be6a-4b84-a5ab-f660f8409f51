load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle" 
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/E2000_solin/Yearly_E2000_solin_01_100_1.nc", "r")

f_1CO2 = addfile(e_path+"/Yearly_F_CO2_01_60_new1.nc", "r")
f_2CO2 = addfile(e_path+"/Yearly_F_2CO2_01_60_new1.nc", "r")
f_UNIF_22_5 = addfile(e_path+"/Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc", "r")
f_SOLAR = addfile("/home/<USER>/Documents/Data_solin/F2000_solin/Yearly_F2000_solin_01_60_1.nc", "r")

; Calculate net radiation for SST runs
NET_RAD_SST_1CO2 = (/f_1CO2->FSNT(30:59,:,:) - f_1CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_2CO2 = (/f_2CO2->FSNT(30:59,:,:) - f_2CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_UNIF_22_5 = (/f_UNIF_22_5->FSNT(30:59,:,:) - f_UNIF_22_5->FLNT(30:59,:,:)/)
NET_RAD_SST_SOLAR = (/f_SOLAR->FSNT(30:59,:,:) - f_SOLAR->FLNT(30:59,:,:)/)

; Calculate net radiation for SOM runs
NET_RAD_SOM_1CO2 = (/e_1CO2->FSNT(40:99,:,:) - e_1CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_2CO2 = (/e_2CO2->FSNT(40:99,:,:) - e_2CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_UNIF_22_5 = (/e_UNIF_22_5->FSNT(40:99,:,:) - e_UNIF_22_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_SOLAR = (/e_SOLAR->FSNT(40:99,:,:) - e_SOLAR->FLNT(40:99,:,:)/)

; Copy coordinates
dummy1 = f_1CO2->TS(:,:,:)
dummy2 = e_1CO2->TS(:,:,:)

copy_VarCoords(dummy1, NET_RAD_SST_1CO2)
copy_VarCoords(dummy1, NET_RAD_SST_2CO2)
copy_VarCoords(dummy1, NET_RAD_SST_UNIF_22_5)
copy_VarCoords(dummy1, NET_RAD_SST_SOLAR)

copy_VarCoords(dummy2, NET_RAD_SOM_1CO2)
copy_VarCoords(dummy2, NET_RAD_SOM_2CO2)
copy_VarCoords(dummy2, NET_RAD_SOM_UNIF_22_5)
copy_VarCoords(dummy2, NET_RAD_SOM_SOLAR)

; Get latitude and longitude
lat = f_1CO2->lat
lon = f_1CO2->lon

; Calculate area weights
jlat = dimsizes(lat)
rad = 4.0*atan(1.0)/180.0
re = 6371220.0
rr = re*rad
dlon = abs(lon(2)-lon(1))*rr
dx = dlon*cos(lat*rad)
dy = new(jlat, typeof(dx))
dy(0) = abs(lat(2)-lat(1))*rr
dy(1:jlat-2) = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5
dy(jlat-1) = abs(lat(jlat-1)-lat(jlat-2))*rr
area = dx*dy
clat = cos(lat*rad)

; Copy coordinates to area
dummy3 = f_1CO2->lat
copy_VarCoords(dummy3, area)

; Calculate weights for area-weighted averaging
weights = conform_dims((/dimsizes(lat), dimsizes(lon)/), clat, 0)

; Time average first for NET_RAD
NET_RAD_SST_2CO2_avg = dim_avg_n_Wrap(NET_RAD_SST_2CO2, 0)  ; [lat, lon]
NET_RAD_SST_1CO2_avg = dim_avg_n_Wrap(NET_RAD_SST_1CO2, 0)
NET_RAD_SST_UNIF_avg = dim_avg_n_Wrap(NET_RAD_SST_UNIF_22_5, 0)
NET_RAD_SST_SOLAR_avg = dim_avg_n_Wrap(NET_RAD_SST_SOLAR, 0)

; Weighted zonal mean for NET_RAD: average across lon (dim 1)
NET_RAD_SST_2CO2_zonal = dim_avg_n_Wrap(NET_RAD_SST_2CO2_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
NET_RAD_SST_1CO2_zonal = dim_avg_n_Wrap(NET_RAD_SST_1CO2_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
NET_RAD_SST_UNIF_zonal = dim_avg_n_Wrap(NET_RAD_SST_UNIF_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
NET_RAD_SST_SOLAR_zonal = dim_avg_n_Wrap(NET_RAD_SST_SOLAR_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)

; NET_RAD difference vs latitude
delta_N_SST_1CO2_lat = NET_RAD_SST_2CO2_zonal - NET_RAD_SST_1CO2_zonal  ; 2CO2 - 1CO2
delta_N_SST_UNIF_22_5_lat = NET_RAD_SST_UNIF_zonal - NET_RAD_SST_2CO2_zonal  ; UNIF - 2CO2
delta_N_SST_SOLAR_lat = NET_RAD_SST_SOLAR_zonal - NET_RAD_SST_2CO2_zonal  ; SOLAR - 2CO2
delta_N_SST_UNIF_22_5_lat1 = NET_RAD_SST_UNIF_zonal - NET_RAD_SST_1CO2_zonal  ; UNIF - 1CO2
delta_N_SST_SOLAR_lat1 = NET_RAD_SST_SOLAR_zonal - NET_RAD_SST_1CO2_zonal  ; SOLAR - 1CO2

; Time average first for temperature
TS_2CO2_avg = dim_avg_n_Wrap(f_2CO2->TS(30:59,:,:), 0)  ; [lat, lon]
TS_1CO2_avg = dim_avg_n_Wrap(f_1CO2->TS(30:59,:,:), 0)
TS_UNIF_avg = dim_avg_n_Wrap(f_UNIF_22_5->TS(30:59,:,:), 0)
TS_SOLAR_avg = dim_avg_n_Wrap(f_SOLAR->TS(30:59,:,:), 0)

; Weighted zonal mean for temperature: average across lon (dim 1)
TS_2CO2_zonal = dim_avg_n_Wrap(TS_2CO2_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
TS_1CO2_zonal = dim_avg_n_Wrap(TS_1CO2_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
TS_UNIF_zonal = dim_avg_n_Wrap(TS_UNIF_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)
TS_SOLAR_zonal = dim_avg_n_Wrap(TS_SOLAR_avg * weights, 1) / dim_avg_n_Wrap(weights, 1)

; Temperature difference vs latitude
DELTA_T_SST_1CO2_lat = TS_2CO2_zonal - TS_1CO2_zonal  ; 2CO2 - 1CO2
DELTA_T_SST_UNIF_22_5_lat = TS_UNIF_zonal - TS_2CO2_zonal  ; UNIF - 2CO2
DELTA_T_SST_SOLAR_lat = TS_SOLAR_zonal - TS_2CO2_zonal  ; SOLAR - 2CO2
DELTA_T_SST_UNIF_22_5_lat1 = TS_UNIF_zonal - TS_1CO2_zonal  ; UNIF - 1CO2
DELTA_T_SST_SOLAR_lat1 = TS_SOLAR_zonal - TS_1CO2_zonal  ; SOLAR - 1CO2

; Calculate global mean differences for SST runs with proper area weighting
DELTA_N_SST_1CO2_GLOBALmean = wgt_areaave_Wrap(NET_RAD_SST_2CO2, area, 1.0, 1) - wgt_areaave_Wrap(NET_RAD_SST_1CO2, area, 1.0, 1)
DELTA_N_SST_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5, area, 1.0, 1) - wgt_areaave_Wrap(NET_RAD_SST_2CO2, area, 1.0, 1)
DELTA_N_SST_SOLAR_GLOBALmean_22_5 = wgt_areaave_Wrap(NET_RAD_SST_SOLAR, area, 1.0, 1) - wgt_areaave_Wrap(NET_RAD_SST_2CO2, area, 1.0, 1)
DELTA_N_SST_UNIF_GLOBALmean_22_5_1 = wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5, area, 1.0, 1) - wgt_areaave_Wrap(NET_RAD_SST_1CO2, area, 1.0, 1)
DELTA_N_SST_SOLAR_GLOBALmean_22_5_1 = wgt_areaave_Wrap(NET_RAD_SST_SOLAR, area, 1.0, 1) - wgt_areaave_Wrap(NET_RAD_SST_1CO2, area, 1.0, 1)

DELTA_T_SST_1CO2_GLOBALmean = wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:), area, 1.0, 1) - wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:), area, 1.0, 1)
DELTA_T_SST_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:), area, 1.0, 1) - wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:), area, 1.0, 1)
DELTA_T_SST_SOLAR_GLOBALmean_22_5 = wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:), area, 1.0, 1) - wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:), area, 1.0, 1)
DELTA_T_SST_UNIF_GLOBALmean_22_5_1 = wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:), area, 1.0, 1) - wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:), area, 1.0, 1)
DELTA_T_SST_SOLAR_GLOBALmean_22_5_1 = wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:), area, 1.0, 1) - wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:), area, 1.0, 1)

; Calculate global mean differences for SOM runs with proper area weighting
DELTA_N_SOM_1CO2_GLOBALmean = wgt_areaave_Wrap(NET_RAD_SOM_2CO2, area, 1.0, 1) - wgt_areaave_Wrap(NET_RAD_SOM_1CO2, area, 1.0, 1)
DELTA_N_SOM_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5, area, 1.0, 1) - wgt_areaave_Wrap(NET_RAD_SOM_2CO2, area, 1.0, 1)
DELTA_N_SOM_SOLAR_GLOBALmean_22_5 = wgt_areaave_Wrap(NET_RAD_SOM_SOLAR, area, 1.0, 1) - wgt_areaave_Wrap(NET_RAD_SOM_2CO2, area, 1.0, 1)
DELTA_N_SOM_UNIF_GLOBALmean_22_5_1 = wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5, area, 1.0, 1) - wgt_areaave_Wrap(NET_RAD_SOM_1CO2, area, 1.0, 1)
DELTA_N_SOM_SOLAR_GLOBALmean_22_5_1 = wgt_areaave_Wrap(NET_RAD_SOM_SOLAR, area, 1.0, 1) - wgt_areaave_Wrap(NET_RAD_SOM_1CO2, area, 1.0, 1)

DELTA_T_SOM_1CO2_GLOBALmean = wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:), area, 1.0, 1) - wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:), area, 1.0, 1)
DELTA_T_SOM_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:), area, 1.0, 1) - wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:), area, 1.0, 1)
DELTA_T_SOM_SOLAR_GLOBALmean_22_5 = wgt_areaave_Wrap(e_SOLAR->TS(40:99,:,:), area, 1.0, 1) - wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:), area, 1.0, 1)
DELTA_T_SOM_UNIF_GLOBALmean_22_5_1 = wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:), area, 1.0, 1) - wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:), area, 1.0, 1)
DELTA_T_SOM_SOLAR_GLOBALmean_22_5_1 = wgt_areaave_Wrap(e_SOLAR->TS(40:99,:,:), area, 1.0, 1) - wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:), area, 1.0, 1)

; Calculate lambda for each forcing type
Lamda_1CO2 = (dim_avg_n_Wrap(DELTA_N_SST_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_1CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SOM_1CO2_GLOBALmean, 0))
Lamda_UNIF_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0))
Lamda_SOLAR = (dim_avg_n_Wrap(DELTA_N_SST_SOLAR_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_SOLAR_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_SOLAR_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_SOLAR_GLOBALmean_22_5, 0))
Lamda_UNIF_22_5_1 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5_1, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5_1, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5_1, 0) - dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5_1, 0))
Lamda_SOLAR_1 = (dim_avg_n_Wrap(DELTA_N_SST_SOLAR_GLOBALmean_22_5_1, 0) - dim_avg_n_Wrap(DELTA_N_SOM_SOLAR_GLOBALmean_22_5_1, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_SOLAR_GLOBALmean_22_5_1, 0) - dim_avg_n_Wrap(DELTA_T_SOM_SOLAR_GLOBALmean_22_5_1, 0))

; Print lambda values
print("Lambda values:")
print("Lambda_1CO2 = " + Lamda_1CO2)
print("Lambda_UNIF_22_5 = " + Lamda_UNIF_22_5)
print("Lambda_SOLAR = " + Lamda_SOLAR)
print("Lambda_UNIF_22_5_1 = " + Lamda_UNIF_22_5_1)
print("Lambda_SOLAR_1 = " + Lamda_SOLAR_1)

; Calculate ERF for each latitude
; Create a new array to hold all five ERF zonal profiles
ERF = new((/5, dimsizes(lat)/), float)

; For 2CO2-1CO2
ERF(0,:) = delta_N_SST_1CO2_lat - Lamda_1CO2 * DELTA_T_SST_1CO2_lat

; For UNIF-2CO2
ERF(1,:) = delta_N_SST_UNIF_22_5_lat - Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5_lat

; For SOLAR-2CO2
ERF(2,:) = delta_N_SST_SOLAR_lat - Lamda_SOLAR * DELTA_T_SST_SOLAR_lat

; For UNIF-1CO2
ERF(3,:) = delta_N_SST_UNIF_22_5_lat1 - Lamda_UNIF_22_5_1 * DELTA_T_SST_UNIF_22_5_lat1

; For SOLAR-1CO2
ERF(4,:) = delta_N_SST_SOLAR_lat1 - Lamda_SOLAR_1 * DELTA_T_SST_SOLAR_lat1

; Calculate global mean ERF values with proper cosine weighting
; For ERF_1CO2
ERF_zonal_1CO2 = delta_N_SST_1CO2_lat - Lamda_1CO2 * DELTA_T_SST_1CO2_lat
ERF_1CO2 = wgt_areaave_Wrap(ERF_zonal_1CO2, clat, 1.0, 0)

; For ERF_UNIF_22_5
ERF_zonal_UNIF_22_5 = delta_N_SST_UNIF_22_5_lat - Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5_lat
ERF_UNIF_22_5 = wgt_areaave_Wrap(ERF_zonal_UNIF_22_5, clat, 1.0, 0)

; For ERF_SOLAR
ERF_zonal_SOLAR = delta_N_SST_SOLAR_lat - Lamda_SOLAR * DELTA_T_SST_SOLAR_lat
ERF_SOLAR = wgt_areaave_Wrap(ERF_zonal_SOLAR, clat, 1.0, 0)

; For ERF_UNIF_1CO2
ERF_zonal_UNIF_1CO2 = delta_N_SST_UNIF_22_5_lat1 - Lamda_UNIF_22_5_1 * DELTA_T_SST_UNIF_22_5_lat1
ERF_UNIF_1CO2 = wgt_areaave_Wrap(ERF_zonal_UNIF_1CO2, clat, 1.0, 0)

; For ERF_SOLAR_1CO2
ERF_zonal_SOLAR_1CO2 = delta_N_SST_SOLAR_lat1 - Lamda_SOLAR_1 * DELTA_T_SST_SOLAR_lat1
ERF_SOLAR_1CO2 = wgt_areaave_Wrap(ERF_zonal_SOLAR_1CO2, clat, 1.0, 0)

; Print the global mean ERF values
print("Global mean ERF values:")
print("2CO2 (relative to 1CO2): " + ERF_1CO2)
print("UNIF (relative to 2CO2): " + ERF_UNIF_22_5)
print("SOLAR (relative to 2CO2): " + ERF_SOLAR)
print("UNIF (relative to 1CO2): " + ERF_UNIF_1CO2)
print("SOLAR (relative to 1CO2): " + ERF_SOLAR_1CO2)

; Create a subset of ERF for plotting
ERF1 = new((/3, dimsizes(lat)/), float)
ERF1(0,:) = ERF_zonal_1CO2
ERF1(1,:) = ERF_zonal_UNIF_22_5
ERF1(2,:) = ERF_zonal_SOLAR
ERF1!0 = "case"
ERF1!1 = "lat"
ERF1&lat = lat

; Print ERF arrays
print("ERF array:")
print(ERF)
print("ERF1 array:")
print(ERF1)

;plotting
;***********************************************************
; Plot: Radiative Forcing vs Latitude
;***********************************************************
wks = gsn_open_wks("pdf","ERF_vs_lat_SCG_area_weighted")   ; or "png" instead of "pdf"

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"red","blue","green","blue","green"/)
res@xyLineThicknesses = (/2.5, 2.5, 2.5, 2.5, 2.5/)
res@xyDashPatterns    = (/0, 0, 0, 1, 1/)         ; solid for first 3, dashed for last 2
res@gsnXYTopLabel     = False
res@tiMainString      = "Effective Radiative Forcing vs Latitude (Area-Weighted)"
res@tiYAxisString     = "ERF (Wm~S~-2~NN~)"
res@tiXAxisString     = "Latitude (~F34~0~F~)"
res@trXMinF           = -90
res@trXMaxF           = 90

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"2xCO~B~2", "SAI-2CO2", "SOLAR-2CO2", "SAI-1CO2", "SOLAR-1CO2"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.25                       ;-- define legend width
res@pmLegendHeightF        =  0.20   
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

plot = gsn_csm_xy(wks, lat, ERF, res)

exit()
  
end
  
  
 
