import matplotlib.pyplot as plt
import numpy as np

# Data for the graph based on the provided table
forcing_types = ['CO₂ Increase', 'Solar Dimming', 'Sulfate SAI']

# Precipitation change values (in percentage)
# [fast_precip_change, slow_precip_change, total_precip_change]
co2_data = [-1.5, -2.0, -3.5]  # CO₂: slight fast decrease, moderate slow decrease
solar_data = [-1.0, -1.5, -2.5]  # Solar: mild fast decrease, mild slow decrease
sai_data = [-4.0, -2.0, -6.0]  # SAI: significant fast decrease, mild slow decrease

# Combine data for plotting
fast_precip = [co2_data[0], solar_data[0], sai_data[0]]
slow_precip = [co2_data[1], solar_data[1], sai_data[1]]
total_precip = [co2_data[2], solar_data[2], sai_data[2]]

# Set up the figure and axis
fig, ax = plt.subplots(figsize=(10, 6))

# Set the width of the bars
width = 0.25

# Set the positions of the bars on the x-axis
r1 = np.arange(len(forcing_types))
r2 = [x + width for x in r1]
r3 = [x + width for x in r2]

# Create the bars
fast_bars = ax.bar(r1, fast_precip, width, label='Fast Precipitation Change', color='#3498db')
slow_bars = ax.bar(r2, slow_precip, width, label='Slow Precipitation Change', color='#e74c3c')
total_bars = ax.bar(r3, total_precip, width, label='Total Precipitation Change', color='#2ecc71')

# Add labels, title, and custom x-axis tick labels
ax.set_xlabel('Forcing Type', fontsize=14)
ax.set_ylabel('Precipitation Change (%)', fontsize=14)
ax.set_title('Generalized Hydrological Sensitivity by Forcing Type', fontsize=16)
ax.set_xticks([r + width for r in range(len(forcing_types))])
ax.set_xticklabels(forcing_types, fontsize=12)

# Add a horizontal grid for better readability
ax.grid(axis='y', linestyle='--', alpha=0.7)

# Add a legend
ax.legend(fontsize=12)

# Add value labels on top of each bar
def add_labels(bars):
    for bar in bars:
        height = bar.get_height()
        ax.annotate(f'{height}%',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),  # 3 points vertical offset
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=10)

add_labels(fast_bars)
add_labels(slow_bars)
add_labels(total_bars)

# Add a horizontal line at y=0
ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)

# Adjust the y-axis to show the negative values properly
ax.set_ylim(min(min(fast_precip), min(slow_precip), min(total_precip)) * 1.2, 1)

# Add a text box with additional information
textstr = '\n'.join((
    'Forcing Type Characteristics:',
    'CO₂: Tropospheric heating, Hadley cell shift',
    'Solar: Minor circulation changes',
    'SAI: Stratospheric heating, Strong tropical circulation weakening'
))
props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
ax.text(0.05, 0.05, textstr, transform=ax.transAxes, fontsize=10,
        verticalalignment='bottom', bbox=props)

# Adjust layout and save the figure
plt.tight_layout()
plt.savefig('hydrological_sensitivity_plot.png', dpi=300)
plt.show()
