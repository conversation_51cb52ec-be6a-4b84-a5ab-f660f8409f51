load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************


; e_path="/Volumes/Backup Plus"
e_path="/home/<USER>/Documents" 
e_1CO2 = addfile(e_path+"/Data/Yearmean_data/SOM/22_5/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Data/Yearmean_data/SOM/22_5/Yearly_E_2CO2_01_100new1.nc", "r")

e_UNIF_22_5 = addfile(e_path+"/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_TROP_22_5 = addfile(e_path+"/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_TROP_01_100_2D_1.nc", "r")
e_POLA_22_5 = addfile(e_path+"/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_POLAR_01_100_2D_1.nc", "r")
e_ARCT_22_5 = addfile(e_path+"/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_ARCTIC_01_100_2D_1.nc", "r")
e_ANTA_22_5 = addfile(e_path+"/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_ANTARC_01_100_2D_1.nc", "r")

e_UNIF_15 = addfile(e_path+"/Data_15MT/YEARLY/Yearly_E2000_2CO2_UNIF_15_01_100_2Dnew1.nc", "r")
e_TROP_15 = addfile(e_path+"/Data_15MT/YEARLY/Yearly_E2000_2CO2_TROP_15_01_100_2Dnew1.nc", "r")
e_POLA_15 = addfile(e_path+"/Data_15MT/YEARLY/Yearly_E2000_2CO2_POL_15_01_100_2Dnew1.nc", "r")
e_ARCT_15 = addfile(e_path+"/Data_15MT/YEARLY/Yearly_E2000_2CO2_ARC_15_01_100_2Dnew1.nc", "r")
e_ANTA_15 = addfile(e_path+"/Data_15MT/YEARLY/Yearly_E2000_2CO2_ANTA_15_01_100_2Dnew1.nc", "r")

e_UNIF_7_5 = addfile(e_path+"/Data_7_5MT/Yearly/Yearly_E2000_2CO2_UNIF_7_5_01_100_2D_1.nc", "r")
e_TROP_7_5 = addfile(e_path+"/Data_7_5MT/Yearly/Yearly_E2000_2CO2_TROP_7_5_01_100_2D_1.nc", "r")
e_POLA_7_5 = addfile(e_path+"/Data_7_5MT/Yearly/Yearly_E2000_2CO2_POLA_7_5_01_100_2D_1.nc", "r")
e_ARCT_7_5 = addfile(e_path+"/Data_7_5MT/Yearly/Yearly_E2000_2CO2_ARCT_7_5_01_100_2D_1.nc", "r")
e_ANTA_7_5 = addfile(e_path+"/Data_7_5MT/Yearly/Yearly_E2000_2CO2_ANTA_7_5_01_100_2D_1.nc", "r")


f_1CO2 = addfile(e_path+"/Data/Yearmean_data/Prescribed_SST/22_5/Yearly_F_CO2_01_60_new1.nc", "r")
f_2CO2 = addfile(e_path+"/Data/Yearmean_data/Prescribed_SST/22_5/Yearly_F_2CO2_01_60_new1.nc", "r")

f_UNIF_22_5 = addfile(e_path+"/Data/Yearmean_data/Prescribed_SST/22_5/Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc", "r")
f_TROP_22_5 = addfile(e_path+"/Data/Yearmean_data/Prescribed_SST/22_5/Yearly_F2000_2CO2_TROP_01_60_2D_1.nc", "r")
f_POLA_22_5 = addfile(e_path+"/Data/Yearmean_data/Prescribed_SST/22_5/Yearly_F2000_2CO2_POLAR_01_60_2D_1.nc", "r")
f_ARCT_22_5 = addfile(e_path+"/Data/Yearmean_data/Prescribed_SST/22_5/Yearly_F2000_2CO2_ARCTIC_01_60_2D_1.nc", "r")
f_ANTA_22_5 = addfile(e_path+"/Data/Yearmean_data/Prescribed_SST/22_5/Yearly_F2000_2CO2_ANTARC_01_60_2D_1.nc", "r")

f_UNIF_15 = addfile(e_path+"/Data_15MT/YEARLY/Yearly_F2000_2CO2_UNIF_15_01_60_2Dnew1.nc", "r")
f_TROP_15 = addfile(e_path+"/Data_15MT/YEARLY/Yearly_F2000_2CO2_TROP_15_01_60_2Dnew1.nc", "r")
f_POLA_15 = addfile(e_path+"/Data_15MT/YEARLY/Yearly_F2000_2CO2_POL_15_01_60_2Dnew1.nc", "r")
f_ARCT_15 = addfile(e_path+"/Data_15MT/YEARLY/Yearly_F2000_2CO2_ARC_15_01_60_2Dnew1.nc", "r")
f_ANTA_15 = addfile(e_path+"/Data_15MT/YEARLY/Yearly_F2000_2CO2_ANTA_15_01_60_2Dnew1.nc", "r")

f_UNIF_7_5 = addfile(e_path+"/Data_7_5MT/Yearly/Yearly_F2000_2CO2_UNIF_7_5_01_60_2D_1.nc", "r")
f_TROP_7_5 = addfile(e_path+"/Data_7_5MT/Yearly/Yearly_F2000_2CO2_TROP_7_5_01_60_2D_1.nc", "r")
f_POLA_7_5 = addfile(e_path+"/Data_7_5MT/Yearly/Yearly_F2000_2CO2_POLA_7_5_01_60_2D_1.nc", "r")
f_ARCT_7_5 = addfile(e_path+"/Data_7_5MT/Yearly/Yearly_F2000_2CO2_ARCT_7_5_01_60_2D_1.nc", "r")
f_ANTA_7_5 = addfile(e_path+"/Data_7_5MT/Yearly/Yearly_F2000_2CO2_ANTA_7_5_01_60_2D_1.nc", "r")


NET_RAD_SST_1CO2          = (/f_1CO2->FSNT(30:59,:,:) - f_1CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_2CO2          = (/f_2CO2->FSNT(30:59,:,:) - f_2CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_UNIF_22_5          = (/f_UNIF_22_5->FSNT(30:59,:,:) - f_UNIF_22_5->FLNT(30:59,:,:)/)
NET_RAD_SST_TROP_22_5          = (/f_TROP_22_5->FSNT(30:59,:,:) - f_TROP_22_5->FLNT(30:59,:,:)/)
NET_RAD_SST_POLA_22_5          = (/f_POLA_22_5->FSNT(30:59,:,:) - f_POLA_22_5->FLNT(30:59,:,:)/)
NET_RAD_SST_ARCT_22_5          = (/f_ARCT_22_5->FSNT(30:59,:,:) - f_ARCT_22_5->FLNT(30:59,:,:)/)
NET_RAD_SST_ANTA_22_5          = (/f_ANTA_22_5->FSNT(30:59,:,:) - f_ANTA_22_5->FLNT(30:59,:,:)/)

NET_RAD_SST_UNIF_15          = (/f_UNIF_15->FSNT(30:59,:,:) - f_UNIF_15->FLNT(30:59,:,:)/)
NET_RAD_SST_TROP_15          = (/f_TROP_15->FSNT(30:59,:,:) - f_TROP_15->FLNT(30:59,:,:)/)
NET_RAD_SST_POLA_15          = (/f_POLA_15->FSNT(30:59,:,:) - f_POLA_15->FLNT(30:59,:,:)/)
NET_RAD_SST_ARCT_15          = (/f_ARCT_15->FSNT(30:59,:,:) - f_ARCT_15->FLNT(30:59,:,:)/)
NET_RAD_SST_ANTA_15          = (/f_ANTA_15->FSNT(30:59,:,:) - f_ANTA_15->FLNT(30:59,:,:)/)

NET_RAD_SST_UNIF_7_5          = (/f_UNIF_7_5->FSNT(30:59,:,:) - f_UNIF_7_5->FLNT(30:59,:,:)/)
NET_RAD_SST_TROP_7_5          = (/f_TROP_7_5->FSNT(30:59,:,:) - f_TROP_7_5->FLNT(30:59,:,:)/)
NET_RAD_SST_POLA_7_5          = (/f_POLA_7_5->FSNT(30:59,:,:) - f_POLA_7_5->FLNT(30:59,:,:)/)
NET_RAD_SST_ARCT_7_5          = (/f_ARCT_7_5->FSNT(30:59,:,:) - f_ARCT_7_5->FLNT(30:59,:,:)/)
NET_RAD_SST_ANTA_7_5          = (/f_ANTA_7_5->FSNT(30:59,:,:) - f_ANTA_7_5->FLNT(30:59,:,:)/)

NET_RAD_SOM_1CO2          = (/e_1CO2->FSNT(40:99,:,:) - e_1CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_2CO2          = (/e_2CO2->FSNT(40:99,:,:) - e_2CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_UNIF_22_5          = (/e_UNIF_22_5->FSNT(40:99,:,:) - e_UNIF_22_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_TROP_22_5          = (/e_TROP_22_5->FSNT(40:99,:,:) - e_TROP_22_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_POLA_22_5         = (/e_POLA_22_5->FSNT(40:99,:,:) - e_POLA_22_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_ARCT_22_5          = (/e_ARCT_22_5->FSNT(40:99,:,:) - e_ARCT_22_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_ANTA_22_5          = (/e_ANTA_22_5->FSNT(40:99,:,:) - e_ANTA_22_5->FLNT(40:99,:,:)/)

NET_RAD_SOM_UNIF_15          = (/e_UNIF_15->FSNT(40:99,:,:) - e_UNIF_15->FLNT(40:99,:,:)/)
NET_RAD_SOM_TROP_15          = (/e_TROP_15->FSNT(40:99,:,:) - e_TROP_15->FLNT(40:99,:,:)/)
NET_RAD_SOM_POLA_15         = (/e_POLA_15->FSNT(40:99,:,:) - e_POLA_15->FLNT(40:99,:,:)/)
NET_RAD_SOM_ARCT_15          = (/e_ARCT_15->FSNT(40:99,:,:) - e_ARCT_15->FLNT(40:99,:,:)/)
NET_RAD_SOM_ANTA_15          = (/e_ANTA_15->FSNT(40:99,:,:) - e_ANTA_15->FLNT(40:99,:,:)/)

NET_RAD_SOM_UNIF_7_5          = (/e_UNIF_7_5->FSNT(40:99,:,:) - e_UNIF_7_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_TROP_7_5          = (/e_TROP_7_5->FSNT(40:99,:,:) - e_TROP_7_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_POLA_7_5          = (/e_POLA_7_5->FSNT(40:99,:,:) - e_POLA_7_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_ARCT_7_5          = (/e_ARCT_7_5->FSNT(40:99,:,:) - e_ARCT_7_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_ANTA_7_5          = (/e_ANTA_7_5->FSNT(40:99,:,:) - e_ANTA_7_5->FLNT(40:99,:,:)/)
;---------------------------------------------------------
dummy1=f_1CO2->TS(:,:,:)
dummy2=e_1CO2->TS(:,:,:)

copy_VarCoords(dummy1,NET_RAD_SST_1CO2)
copy_VarCoords(dummy1,NET_RAD_SST_2CO2)
copy_VarCoords(dummy1,NET_RAD_SST_UNIF_22_5)
copy_VarCoords(dummy1,NET_RAD_SST_TROP_22_5)
copy_VarCoords(dummy1,NET_RAD_SST_POLA_22_5)
copy_VarCoords(dummy1,NET_RAD_SST_ARCT_22_5)
copy_VarCoords(dummy1,NET_RAD_SST_ANTA_22_5)
copy_VarCoords(dummy1,NET_RAD_SST_UNIF_15)
copy_VarCoords(dummy1,NET_RAD_SST_TROP_15)
copy_VarCoords(dummy1,NET_RAD_SST_POLA_15)
copy_VarCoords(dummy1,NET_RAD_SST_ARCT_15)
copy_VarCoords(dummy1,NET_RAD_SST_ANTA_15)
copy_VarCoords(dummy1,NET_RAD_SST_UNIF_7_5)
copy_VarCoords(dummy1,NET_RAD_SST_TROP_7_5)
copy_VarCoords(dummy1,NET_RAD_SST_POLA_7_5)
copy_VarCoords(dummy1,NET_RAD_SST_ARCT_7_5)
copy_VarCoords(dummy1,NET_RAD_SST_ANTA_7_5)

copy_VarCoords(dummy2,NET_RAD_SOM_1CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_2CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_UNIF_22_5)
copy_VarCoords(dummy2,NET_RAD_SOM_TROP_22_5)
copy_VarCoords(dummy2,NET_RAD_SOM_POLA_22_5)
copy_VarCoords(dummy2,NET_RAD_SOM_ARCT_22_5)
copy_VarCoords(dummy2,NET_RAD_SOM_ANTA_22_5)
copy_VarCoords(dummy2,NET_RAD_SOM_UNIF_15)
copy_VarCoords(dummy2,NET_RAD_SOM_TROP_15)
copy_VarCoords(dummy2,NET_RAD_SOM_POLA_15)
copy_VarCoords(dummy2,NET_RAD_SOM_ARCT_15)
copy_VarCoords(dummy2,NET_RAD_SOM_ANTA_15)
copy_VarCoords(dummy2,NET_RAD_SOM_UNIF_7_5)
copy_VarCoords(dummy2,NET_RAD_SOM_TROP_7_5)
copy_VarCoords(dummy2,NET_RAD_SOM_POLA_7_5)
copy_VarCoords(dummy2,NET_RAD_SOM_ARCT_7_5)
copy_VarCoords(dummy2,NET_RAD_SOM_ANTA_7_5)
;******************************************                 Areal average
lat=f_1CO2->lat
lon=f_1CO2->lon

  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************   
dummy3=f_1CO2->lat
copy_VarCoords(dummy3,area)
print(area)


DELTA_N_SST_1CO2 = (wgt_areaave_Wrap(NET_RAD_SST_1CO2(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_UNIF_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_TROP_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_TROP_22_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_POLA_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_POLA_22_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_ARCT_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_ARCT_22_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_ANTA_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_ANTA_22_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_UNIF_15 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_15(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_TROP_15 = (wgt_areaave_Wrap(NET_RAD_SST_TROP_15(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_POLA_15 = (wgt_areaave_Wrap(NET_RAD_SST_POLA_15(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_ARCT_15 = (wgt_areaave_Wrap(NET_RAD_SST_ARCT_15(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_ANTA_15 = (wgt_areaave_Wrap(NET_RAD_SST_ANTA_15(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_UNIF_7_5 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_7_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_TROP_7_5 = (wgt_areaave_Wrap(NET_RAD_SST_TROP_7_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_POLA_7_5 = (wgt_areaave_Wrap(NET_RAD_SST_POLA_7_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_ARCT_7_5 = (wgt_areaave_Wrap(NET_RAD_SST_ARCT_7_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))
DELTA_N_SST_ANTA_7_5 = (wgt_areaave_Wrap(NET_RAD_SST_ANTA_7_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1))

DELTA_T_SST_1CO2 = (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_UNIF_22_5 = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_TROP_22_5 = (wgt_areaave_Wrap(f_TROP_22_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_POLA_22_5 = (wgt_areaave_Wrap(f_POLA_22_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_ARCT_22_5 = (wgt_areaave_Wrap(f_ARCT_22_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_ANTA_22_5 = (wgt_areaave_Wrap(f_ANTA_22_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_UNIF_15 = (wgt_areaave_Wrap(f_UNIF_15->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_TROP_15 = (wgt_areaave_Wrap(f_TROP_15->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_POLA_15 = (wgt_areaave_Wrap(f_POLA_15->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_ARCT_15 = (wgt_areaave_Wrap(f_ARCT_15->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_ANTA_15 = (wgt_areaave_Wrap(f_ANTA_15->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_UNIF_7_5 = (wgt_areaave_Wrap(f_UNIF_7_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_TROP_7_5 = (wgt_areaave_Wrap(f_TROP_7_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_POLA_7_5 = (wgt_areaave_Wrap(f_POLA_7_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_ARCT_7_5 = (wgt_areaave_Wrap(f_ARCT_7_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))
DELTA_T_SST_ANTA_7_5 = (wgt_areaave_Wrap(f_ANTA_7_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1))

DELTA_N_SST_1CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_TROP_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_TROP_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_POLA_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_POLA_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_ARCT_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_ARCT_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_ANTA_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_ANTA_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_UNIF_GLOBALmean_15 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_15,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_TROP_GLOBALmean_15 = (wgt_areaave_Wrap(NET_RAD_SST_TROP_15,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_POLA_GLOBALmean_15 = (wgt_areaave_Wrap(NET_RAD_SST_POLA_15,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_ARCT_GLOBALmean_15 = (wgt_areaave_Wrap(NET_RAD_SST_ARCT_15,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_ANTA_GLOBALmean_15 = (wgt_areaave_Wrap(NET_RAD_SST_ANTA_15,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_UNIF_GLOBALmean_7_5 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_7_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_TROP_GLOBALmean_7_5 = (wgt_areaave_Wrap(NET_RAD_SST_TROP_7_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_POLA_GLOBALmean_7_5 = (wgt_areaave_Wrap(NET_RAD_SST_POLA_7_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_ARCT_GLOBALmean_7_5 = (wgt_areaave_Wrap(NET_RAD_SST_ARCT_7_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))
DELTA_N_SST_ANTA_GLOBALmean_7_5 = (wgt_areaave_Wrap(NET_RAD_SST_ANTA_7_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1))

DELTA_T_SST_1CO2_GLOBALmean = (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_TROP_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_TROP_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_POLA_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_POLA_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_ARCT_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_ARCT_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_ANTA_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_ANTA_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_UNIF_GLOBALmean_15 = (wgt_areaave_Wrap(f_UNIF_15->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_TROP_GLOBALmean_15 = (wgt_areaave_Wrap(f_TROP_15->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_POLA_GLOBALmean_15 = (wgt_areaave_Wrap(f_POLA_15->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_ARCT_GLOBALmean_15 = (wgt_areaave_Wrap(f_ARCT_15->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_ANTA_GLOBALmean_15 = (wgt_areaave_Wrap(f_ANTA_15->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_UNIF_GLOBALmean_7_5 = (wgt_areaave_Wrap(f_UNIF_7_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_TROP_GLOBALmean_7_5 = (wgt_areaave_Wrap(f_TROP_7_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_POLA_GLOBALmean_7_5 = (wgt_areaave_Wrap(f_POLA_7_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_ARCT_GLOBALmean_7_5 = (wgt_areaave_Wrap(f_ARCT_7_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_ANTA_GLOBALmean_7_5 = (wgt_areaave_Wrap(f_ANTA_7_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1))

DELTA_N_SOM_1CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_TROP_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_TROP_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_POLA_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_POLA_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_ARCT_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_ARCT_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_ANTA_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_ANTA_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_UNIF_GLOBALmean_15 = (wgt_areaave_Wrap(NET_RAD_SOM_UNIF_15,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_TROP_GLOBALmean_15 = (wgt_areaave_Wrap(NET_RAD_SOM_TROP_15,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_POLA_GLOBALmean_15 = (wgt_areaave_Wrap(NET_RAD_SOM_POLA_15,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_ARCT_GLOBALmean_15 = (wgt_areaave_Wrap(NET_RAD_SOM_ARCT_15,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_ANTA_GLOBALmean_15 = (wgt_areaave_Wrap(NET_RAD_SOM_ANTA_15,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_UNIF_GLOBALmean_7_5 = (wgt_areaave_Wrap(NET_RAD_SOM_UNIF_7_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_TROP_GLOBALmean_7_5 = (wgt_areaave_Wrap(NET_RAD_SOM_TROP_7_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_POLA_GLOBALmean_7_5 = (wgt_areaave_Wrap(NET_RAD_SOM_POLA_7_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_ARCT_GLOBALmean_7_5 = (wgt_areaave_Wrap(NET_RAD_SOM_ARCT_7_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))
DELTA_N_SOM_ANTA_GLOBALmean_7_5 = (wgt_areaave_Wrap(NET_RAD_SOM_ANTA_7_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1))

DELTA_T_SOM_1CO2_GLOBALmean = (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_TROP_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_TROP_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_POLA_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_POLA_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_ARCT_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_ARCT_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_ANTA_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_ANTA_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_UNIF_GLOBALmean_15 = (wgt_areaave_Wrap(e_UNIF_15->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_TROP_GLOBALmean_15 = (wgt_areaave_Wrap(e_TROP_15->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_POLA_GLOBALmean_15 = (wgt_areaave_Wrap(e_POLA_15->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_ARCT_GLOBALmean_15 = (wgt_areaave_Wrap(e_ARCT_15->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_ANTA_GLOBALmean_15 = (wgt_areaave_Wrap(e_ANTA_15->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_UNIF_GLOBALmean_7_5 = (wgt_areaave_Wrap(e_UNIF_7_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_TROP_GLOBALmean_7_5 = (wgt_areaave_Wrap(e_TROP_7_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_POLA_GLOBALmean_7_5 = (wgt_areaave_Wrap(e_POLA_7_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_ARCT_GLOBALmean_7_5 = (wgt_areaave_Wrap(e_ARCT_7_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_ANTA_GLOBALmean_7_5 = (wgt_areaave_Wrap(e_ANTA_7_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1))

; Lamda_1CO2 = (dim_avg_n_Wrap(DELTA_N_SST_1CO2_GLOBALmean(30:59), 0) - dim_avg_n_Wrap(DELTA_N_SOM_1CO2_GLOBALmean(40:99), 0)) / (dim_avg_n_Wrap(DELTA_T_SST_1CO2_GLOBALmean(30:59), 0) - dim_avg_n_Wrap(DELTA_T_SOM_1CO2_GLOBALmean(40:99), 0))
; ERF_1CO2 = dim_avg_n_Wrap((DELTA_N_SST_1CO2(30:59) - (Lamda_1CO2 * DELTA_T_SST_1CO2(30:59))),0)
printVarSummary(DELTA_T_SST_1CO2)
Lamda_1CO2 = (dim_avg_n_Wrap(DELTA_N_SST_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_1CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SOM_1CO2_GLOBALmean, 0))
Lamda_UNIF_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0))
Lamda_TROP_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_TROP_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_TROP_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_TROP_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_TROP_GLOBALmean_22_5, 0))
Lamda_POLA_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_POLA_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_POLA_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_POLA_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_POLA_GLOBALmean_22_5, 0))
Lamda_ARCT_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_ARCT_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_ARCT_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_ARCT_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_ARCT_GLOBALmean_22_5, 0))
Lamda_ANTA_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_ANTA_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_ANTA_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_ANTA_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_ANTA_GLOBALmean_22_5, 0))
Lamda_UNIF_15 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_15, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_15, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_15, 0) - dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_15, 0))
Lamda_TROP_15 = (dim_avg_n_Wrap(DELTA_N_SST_TROP_GLOBALmean_15, 0) - dim_avg_n_Wrap(DELTA_N_SOM_TROP_GLOBALmean_15, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_TROP_GLOBALmean_15, 0) - dim_avg_n_Wrap(DELTA_T_SOM_TROP_GLOBALmean_15, 0))
Lamda_POLA_15 = (dim_avg_n_Wrap(DELTA_N_SST_POLA_GLOBALmean_15, 0) - dim_avg_n_Wrap(DELTA_N_SOM_POLA_GLOBALmean_15, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_POLA_GLOBALmean_15, 0) - dim_avg_n_Wrap(DELTA_T_SOM_POLA_GLOBALmean_15, 0))
Lamda_ARCT_15 = (dim_avg_n_Wrap(DELTA_N_SST_ARCT_GLOBALmean_15, 0) - dim_avg_n_Wrap(DELTA_N_SOM_ARCT_GLOBALmean_15, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_ARCT_GLOBALmean_15, 0) - dim_avg_n_Wrap(DELTA_T_SOM_ARCT_GLOBALmean_15, 0))
Lamda_ANTA_15 = (dim_avg_n_Wrap(DELTA_N_SST_ANTA_GLOBALmean_15, 0) - dim_avg_n_Wrap(DELTA_N_SOM_ANTA_GLOBALmean_15, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_ANTA_GLOBALmean_15, 0) - dim_avg_n_Wrap(DELTA_T_SOM_ANTA_GLOBALmean_15, 0))
Lamda_UNIF_7_5 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_7_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_7_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_7_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_7_5, 0))
Lamda_TROP_7_5 = (dim_avg_n_Wrap(DELTA_N_SST_TROP_GLOBALmean_7_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_TROP_GLOBALmean_7_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_TROP_GLOBALmean_7_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_TROP_GLOBALmean_7_5, 0))
Lamda_POLA_7_5 = (dim_avg_n_Wrap(DELTA_N_SST_POLA_GLOBALmean_7_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_POLA_GLOBALmean_7_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_POLA_GLOBALmean_7_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_POLA_GLOBALmean_7_5, 0))
Lamda_ARCT_7_5 = (dim_avg_n_Wrap(DELTA_N_SST_ARCT_GLOBALmean_7_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_ARCT_GLOBALmean_7_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_ARCT_GLOBALmean_7_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_ARCT_GLOBALmean_7_5, 0))
Lamda_ANTA_7_5 = (dim_avg_n_Wrap(DELTA_N_SST_ANTA_GLOBALmean_7_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_ANTA_GLOBALmean_7_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_ANTA_GLOBALmean_7_5, 0) - dim_avg_n_Wrap(DELTA_T_SOM_ANTA_GLOBALmean_7_5, 0))



ERF_1CO2 = dim_avg_n_Wrap((DELTA_N_SST_1CO2 - (Lamda_1CO2 * DELTA_T_SST_1CO2)),0)
ERF_UNIF_22_5 = dim_avg_n_Wrap((DELTA_N_SST_UNIF_22_5 - (Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5)),0)
ERF_TROP_22_5 = dim_avg_n_Wrap((DELTA_N_SST_TROP_22_5 - (Lamda_TROP_22_5 * DELTA_T_SST_TROP_22_5)),0)
ERF_POLA_22_5 = dim_avg_n_Wrap((DELTA_N_SST_POLA_22_5 - (Lamda_POLA_22_5 * DELTA_T_SST_POLA_22_5)),0)
ERF_ARCT_22_5 = dim_avg_n_Wrap((DELTA_N_SST_ARCT_22_5 - (Lamda_ARCT_22_5 * DELTA_T_SST_ARCT_22_5)),0)
ERF_ANTA_22_5 = dim_avg_n_Wrap((DELTA_N_SST_ANTA_22_5 - (Lamda_ANTA_22_5 * DELTA_T_SST_ANTA_22_5)),0)
ERF_UNIF_15 = dim_avg_n_Wrap((DELTA_N_SST_UNIF_15 - (Lamda_UNIF_15 * DELTA_T_SST_UNIF_15)),0)
ERF_TROP_15 = dim_avg_n_Wrap((DELTA_N_SST_TROP_15 - (Lamda_TROP_15 * DELTA_T_SST_TROP_15)),0)
ERF_POLA_15 = dim_avg_n_Wrap((DELTA_N_SST_POLA_15 - (Lamda_POLA_15 * DELTA_T_SST_POLA_15)),0)
ERF_ARCT_15 = dim_avg_n_Wrap((DELTA_N_SST_ARCT_15 - (Lamda_ARCT_15 * DELTA_T_SST_ARCT_15)),0)
ERF_ANTA_15 = dim_avg_n_Wrap((DELTA_N_SST_ANTA_15 - (Lamda_ANTA_15 * DELTA_T_SST_ANTA_15)),0)
ERF_UNIF_7_5 = dim_avg_n_Wrap((DELTA_N_SST_UNIF_7_5 - (Lamda_UNIF_7_5 * DELTA_T_SST_UNIF_7_5)),0)
ERF_TROP_7_5 = dim_avg_n_Wrap((DELTA_N_SST_TROP_7_5 - (Lamda_TROP_7_5 * DELTA_T_SST_TROP_7_5)),0)
ERF_POLA_7_5 = dim_avg_n_Wrap((DELTA_N_SST_POLA_7_5 - (Lamda_POLA_7_5 * DELTA_T_SST_POLA_7_5)),0)
ERF_ARCT_7_5 = dim_avg_n_Wrap((DELTA_N_SST_ARCT_7_5 - (Lamda_ARCT_7_5 * DELTA_T_SST_ARCT_7_5)),0)
ERF_ANTA_7_5 = dim_avg_n_Wrap((DELTA_N_SST_ANTA_7_5 - (Lamda_ANTA_7_5 * DELTA_T_SST_ANTA_7_5)),0)
 
ERF=new((/19/),typeof(dummy2),dummy2@_FillValue)
ERF(0) = dim_avg_n_Wrap((DELTA_N_SST_1CO2 - (Lamda_1CO2 * DELTA_T_SST_1CO2)),0)
ERF(1) = dim_avg_n_Wrap((DELTA_N_SST_UNIF_22_5 - (Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5)),0)
ERF(2) = dim_avg_n_Wrap((DELTA_N_SST_TROP_22_5 - (Lamda_TROP_22_5 * DELTA_T_SST_TROP_22_5)),0)
ERF(3) = dim_avg_n_Wrap((DELTA_N_SST_POLA_22_5 - (Lamda_POLA_22_5 * DELTA_T_SST_POLA_22_5)),0)
ERF(4) = dim_avg_n_Wrap((DELTA_N_SST_ARCT_22_5 - (Lamda_ARCT_22_5 * DELTA_T_SST_ARCT_22_5)),0)
ERF(5) = dim_avg_n_Wrap((DELTA_N_SST_ANTA_22_5 - (Lamda_ANTA_22_5 * DELTA_T_SST_ANTA_22_5)),0)
ERF(6) = dim_avg_n_Wrap((DELTA_N_SST_UNIF_15 - (Lamda_UNIF_15 * DELTA_T_SST_UNIF_15)),0)
ERF(7) = dim_avg_n_Wrap((DELTA_N_SST_TROP_15 - (Lamda_TROP_15 * DELTA_T_SST_TROP_15)),0)
ERF(8) = dim_avg_n_Wrap((DELTA_N_SST_POLA_15 - (Lamda_POLA_15 * DELTA_T_SST_POLA_15)),0)
ERF(9) = dim_avg_n_Wrap((DELTA_N_SST_ARCT_15 - (Lamda_ARCT_15 * DELTA_T_SST_ARCT_15)),0)
ERF(10)= dim_avg_n_Wrap((DELTA_N_SST_ANTA_15 - (Lamda_ANTA_15 * DELTA_T_SST_ANTA_15)),0)
ERF(11) = dim_avg_n_Wrap((DELTA_N_SST_UNIF_7_5 - (Lamda_UNIF_7_5 * DELTA_T_SST_UNIF_7_5)),0)
ERF(12) = dim_avg_n_Wrap((DELTA_N_SST_TROP_7_5 - (Lamda_TROP_7_5 * DELTA_T_SST_TROP_7_5)),0)
ERF(13)= dim_avg_n_Wrap((DELTA_N_SST_POLA_7_5 - (Lamda_POLA_7_5 * DELTA_T_SST_POLA_7_5)),0)
ERF(14) = dim_avg_n_Wrap((DELTA_N_SST_ARCT_7_5 - (Lamda_ARCT_7_5 * DELTA_T_SST_ARCT_7_5)),0)
ERF(15) = dim_avg_n_Wrap((DELTA_N_SST_ANTA_7_5 - (Lamda_ANTA_7_5 * DELTA_T_SST_ANTA_7_5)),0)
ERF(16)=(ERF(1)+ERF(2)+ERF(3))/3 
ERF(17)=(ERF(6)+ERF(7)+ERF(8))/3 
ERF(18)=(ERF(11)+ERF(12)+ERF(13))/3 



; ERF_1CO2_1 = dim_avg_n_Wrap((DELTA_N_SST_1CO2(30:59) - (Lamda_1CO2 * DELTA_T_SST_1CO2(30:59))),0)

;  print(Label_Global_mean)
;------------------------------------------------ 
; asciiwrite("Change_AOD_mean_rectangular_grid.txt",Label_Global_mean)   
; asciiwrite("Change_AOD_mean_rectangular_grid_2point.txt",Label_Global_mean1)   
; asciiwrite("Change_AOD_mean_rectangular_grid_all.txt",Global_mean)   
; print(Lamda_UNIF_22_5)
print(ERF)
print(DELTA_T_SST_1CO2 )
; print((dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0)))

; asciiwrite("ERF_globalmean_rectangular_grid_all.txt",ERF)   
asciiwrite("ERF_globalmean_rectangular_grid_all_new_2lev.txt",ERF)   

  exit()
  
end
  
  
 
