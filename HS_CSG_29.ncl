e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
e_UNIF = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/29_4_solin/E2000_solin_29_4/Yearly_E2000_solin_29_4_01_48_1.nc", "r")



f_1CO2 = addfile(e_path+"/Yearly_F_CO2_01_60_new1.nc", "r")
f_2CO2 = addfile(e_path+"/Yearly_F_2CO2_01_60_new1.nc", "r")
f_UNIF = addfile(e_path+"/Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc", "r")
f_SOLAR = addfile("/home/<USER>/Documents/Data_solin/29_4_solin/F2000_solin_29_4/Yearly_F2000_solin_29_4_01_60_1.nc", "r")

;PT
PT_1CO2_som=e_1CO2->PRECT(40:99,:,:)*8.64e+7
PT_2CO2_som=e_2CO2->PRECT(40:99,:,:)*8.64e+7
PT_UNIF_som=e_UNIF->PRECT(40:99,:,:)*8.64e+7
PT_SOLAR_som=e_SOLAR->PRECT(40::,:,:)*8.64e+7

PT_1CO2_sst=f_1CO2->PRECT(30:59,:,:)*8.64e+7
PT_2CO2_sst=f_2CO2->PRECT(30:59,:,:)*8.64e+7
PT_UNIF_sst=f_UNIF->PRECT(30:59,:,:)*8.64e+7
PT_SOLAR_sst=f_SOLAR->PRECT(30:59,:,:)*8.64e+7


P_1CO2_som=dim_avg_n_Wrap(PT_1CO2_som,0)
P_1CO2_sst=dim_avg_n_Wrap(PT_1CO2_sst,0)

Delta_P_1CO2_som=dim_avg_n_Wrap(PT_1CO2_som-PT_2CO2_som,0)
Delta_P_UNIF_som=dim_avg_n_Wrap(PT_UNIF_som-PT_2CO2_som,0)
Delta_P_SOLAR_som=dim_avg_n_Wrap(PT_SOLAR_som-PT_2CO2_som,0)

Delta_P_1CO2_sst=dim_avg_n_Wrap(PT_1CO2_sst-PT_2CO2_sst,0)
Delta_P_UNIF_sst=dim_avg_n_Wrap(PT_UNIF_sst-PT_2CO2_sst,0)
Delta_P_SOLAR_sst=dim_avg_n_Wrap(PT_SOLAR_sst-PT_2CO2_sst,0)

;TS
TS_1CO2_som=e_1CO2->TS(40:99,:,:)
TS_2CO2_som=e_2CO2->TS(40:99,:,:)
TS_UNIF_som=e_UNIF->TS(40:99,:,:)
TS_SOLAR_som=e_SOLAR->TS(40::,:,:)

TS_1CO2_sst=f_1CO2->TS(30:59,:,:)
TS_2CO2_sst=f_2CO2->TS(30:59,:,:)
TS_UNIF_sst=f_UNIF->TS(30:59,:,:)
TS_SOLAR_sst=f_SOLAR->TS(30:59,:,:)

printVarSummary(TS_SOLAR_som)
Delta_TS_1CO2_som=dim_avg_n_Wrap(TS_1CO2_som-TS_2CO2_som,0)
Delta_TS_UNIF_som=dim_avg_n_Wrap(TS_UNIF_som-TS_2CO2_som,0)
Delta_TS_SOLAR_som=dim_avg_n_Wrap(TS_SOLAR_som-TS_1CO2_som,0)

Delta_TS_1CO2_sst=dim_avg_n_Wrap(TS_1CO2_sst-TS_2CO2_sst,0)
Delta_TS_UNIF_sst=dim_avg_n_Wrap(TS_UNIF_sst-TS_2CO2_sst,0)
Delta_TS_SOLAR_sst=dim_avg_n_Wrap(TS_SOLAR_sst-TS_1CO2_sst,0)

a_ts_som=TS_1CO2_som-TS_2CO2_som
b_PT_som=(PT_1CO2_som-PT_2CO2_som)
;HS_1CO2=(deltaP_som-deltaP_sst)/(deltaT_som-deltaT_sst)
lat=f_1CO2->lat
lon=f_1CO2->lon

  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************
dummy1=f_1CO2->lat
copy_VarCoords(dummy1,area)

delta_PT_1CO2_SOM_mean=((wgt_areaave_Wrap(Delta_P_1CO2_som,area,1.0,1))/wgt_areaave_Wrap(P_1CO2_som,area,1.0,1))*100
delta_PT_UNIF_som_mean=((wgt_areaave_Wrap(Delta_P_UNIF_som,area,1.0,1))/wgt_areaave_Wrap(P_1CO2_som,area,1.0,1))*100
delta_PT_SOLAR_som_mean=((wgt_areaave_Wrap(Delta_P_SOLAR_som,area,1.0,1))/wgt_areaave_Wrap(P_1CO2_som,area,1.0,1))*100

delta_PT_1CO2_SST_mean=((wgt_areaave_Wrap(Delta_P_1CO2_sst,area,1.0,1))/wgt_areaave_Wrap(P_1CO2_sst,area,1.0,1))*100
delta_PT_UNIF_sst_mean=((wgt_areaave_Wrap(Delta_P_UNIF_sst,area,1.0,1))/wgt_areaave_Wrap(P_1CO2_sst,area,1.0,1))*100
delta_PT_SOLAR_sst_mean=((wgt_areaave_Wrap(Delta_P_SOLAR_sst,area,1.0,1))/wgt_areaave_Wrap(P_1CO2_sst,area,1.0,1))*100

printVarSummary(delta_PT_UNIF_sst_mean)
delta_TS_1CO2_SOM_mean=(wgt_areaave_Wrap(Delta_TS_1CO2_som,area,1.0,1))
delta_TS_UNIF_som_mean=(wgt_areaave_Wrap(Delta_TS_UNIF_som,area,1.0,1))
delta_TS_SOLAR_som_mean=(wgt_areaave_Wrap(Delta_TS_SOLAR_som,area,1.0,1))

delta_TS_1CO2_SST_mean=(wgt_areaave_Wrap(Delta_TS_1CO2_sst,area,1.0,1))
delta_TS_UNIF_sst_mean=(wgt_areaave_Wrap(Delta_TS_UNIF_sst,area,1.0,1))
delta_TS_SOLAR_sst_mean=(wgt_areaave_Wrap(Delta_TS_SOLAR_sst,area,1.0,1))

HS_1CO2=(delta_PT_1CO2_SOM_mean-delta_PT_1CO2_SST_mean)/(delta_TS_1CO2_SOM_mean-delta_TS_1CO2_SST_mean)
HS_UNIF=(delta_PT_UNIF_som_mean-delta_PT_UNIF_sst_mean)/(delta_TS_UNIF_som_mean-delta_TS_UNIF_sst_mean)
HS_SOLAR=(delta_PT_SOLAR_som_mean-delta_PT_SOLAR_sst_mean)/(delta_TS_SOLAR_som_mean-delta_TS_SOLAR_sst_mean)

; print(delta_TS_POLA_SST_mean)
print(HS_1CO2)
print(HS_UNIF)
print(HS_SOLAR)


a_ts_som1=(wgt_areaave_Wrap(a_ts_som,area,1.0,1))
b_PT_som1=((wgt_areaave_Wrap(b_PT_som, area, 1.0, 1))/(wgt_areaave_Wrap(P_1CO2_som,area,1.0,1))*100)
res = regline(a_ts_som1,b_PT_som1)
; Extract the slope and intercept from the result
; slope = res(0)    ; Slope of the regression line
; intercept = res(1)  ; Intercept of the regression line
print(res)
; print(intercept)
AHS_1CO2=(delta_PT_1CO2_SOM_mean)/(delta_TS_1CO2_SOM_mean)
AHS_UNIF=(delta_PT_UNIF_som_mean)/(delta_TS_UNIF_som_mean)
AHS_SOLAR=(delta_PT_SOLAR_som_mean)/(delta_TS_SOLAR_som_mean)

; print(AHS_1CO2)
print(AHS_UNIF)
; print(AHS_SOLAR)

; slope and  intercept
; slope_1CO2_HS = (delta_PT_1CO2_SOM_mean - delta_PT_1CO2_SST_mean) / (delta_TS_1CO2_SOM_mean - delta_TS_1CO2_SST_mean)
; intercept_1CO2_HS = delta_PT_1CO2_SOM_mean - slope_1CO2_HS * delta_TS_1CO2_SOM_mean
; print(slope_1CO2_HS)
; print(intercept_1CO2_HS)

slope_UNIF_HS = (delta_PT_UNIF_som_mean - delta_PT_UNIF_sst_mean) / (delta_TS_UNIF_som_mean - delta_TS_UNIF_sst_mean)
 intercept_UNIF_HS = delta_PT_UNIF_som_mean - slope_UNIF_HS * delta_TS_UNIF_som_mean
print(slope_UNIF_HS)
print(intercept_UNIF_HS)

;slope_SOLAR_HS = (delta_PT_SOLAR_som_mean - delta_PT_SOLAR_sst_mean) / (delta_TS_SOLAR_som_mean - delta_TS_SOLAR_sst_mean)
;intercept_SOLAR_HS = delta_PT_SOLAR_som_mean - slope_SOLAR_HS * delta_TS_SOLAR_som_mean
;print(slope_SOLAR_HS)
;print(intercept_SOLAR_HS)

; slope_1CO2_AHS = (delta_PT_1CO2_SOM_mean) / (delta_TS_1CO2_SOM_mean)
; intercept_1CO2_AHS = delta_PT_1CO2_SOM_mean - slope_1CO2_AHS * delta_TS_1CO2_SOM_mean
; print(slope_1CO2_AHS)
; print(intercept_1CO2_AHS)

; slope_UNIF_AHS = (delta_PT_UNIF_SOM_mean) / (delta_TS_UNIF_SOM_mean)
; intercept_UNIF_AHS = delta_PT_UNIF_SOM_mean - slope_UNIF_AHS * delta_TS_UNIF_SOM_mean
; print(slope_UNIF_AHS)
; print(intercept_UNIF_AHS)

; slope_POLA_AHS = (delta_PT_POLA_SOM_mean) / (delta_TS_POLA_SOM_mean)
; intercept_POLA_AHS = delta_PT_POLA_SOM_mean - slope_POLA_AHS * delta_TS_POLA_SOM_mean
; print(slope_POLA_AHS)
; print(intercept_POLA_AHS)


; slope_TROP_HS = (delta_PT_TROP_SOM_mean - delta_PT_TROP_SST_mean) / (delta_TS_TROP_SOM_mean - delta_TS_TROP_SST_mean)
; intercept_TROP_HS = delta_PT_TROP_SOM_mean - slope_TROP_HS * delta_TS_TROP_SOM_mean
; print(slope_TROP_HS)
; print(intercept_TROP_HS)
; slope_TROP_AHS = (delta_PT_TROP_SOM_mean) / (delta_TS_TROP_SOM_mean)
; intercept_TROP_AHS = delta_PT_TROP_SOM_mean - slope_TROP_AHS * delta_TS_TROP_SOM_mean
; print(slope_TROP_AHS)
; print(intercept_TROP_AHS)
printVarSummary(PT_1CO2_som)
a=(wgt_areaave_Wrap(PT_1CO2_som,area,1.0,1))
; printVarSummary(a)
b=(wgt_areaave_Wrap(PT_2CO2_som,area,1.0,1))
c=(wgt_areaave_Wrap(PT_UNIF_som,area,1.0,1))
d=(wgt_areaave_Wrap(PT_SOLAR_som,area,1.0,1))
; e=(wgt_areaave_Wrap(PT_TROP_som,area,1.0,1))

f=(wgt_areaave_Wrap(PT_1CO2_sst,area,1.0,1))
; printVarSummary(a)
g=(wgt_areaave_Wrap(PT_2CO2_sst,area,1.0,1))
h=(wgt_areaave_Wrap(PT_UNIF_sst,area,1.0,1))
i=(wgt_areaave_Wrap(PT_SOLAR_sst,area,1.0,1))
; j=(wgt_areaave_Wrap(PT_TROP_sst,area,1.0,1))
printVarSummary(a)
 ;asciiwrite ("./PT_1CO2_som.txt" ,  a)
 ;asciiwrite ("./PT_2CO2_som.txt" ,  b)
 ;asciiwrite ("./PT_UNIF_som.txt" ,  c)
asciiwrite ("./PT_SOLAR_som_29.txt" ,  d)
; ; asciiwrite ("./PT_TROP_som.txt" ,  e)

 ;asciiwrite ("./PT_1CO2_sst.txt" ,  f)
;asciiwrite ("./PT_2CO2_sst.txt" ,  g)
 ;asciiwrite ("./PT_UNIF_sst.txt" ,  h)
asciiwrite ("./PT_SOLAR_sst_29.txt" ,  i)

; ; asciiwrite ("./PT_TROP_sst.txt" ,  j)


aa=(wgt_areaave_Wrap(TS_1CO2_som,area,1.0,1))
; printVarSummary(a)
bb=(wgt_areaave_Wrap(TS_2CO2_som,area,1.0,1))
cc=(wgt_areaave_Wrap(TS_UNIF_som,area,1.0,1))
dd=(wgt_areaave_Wrap(TS_SOLAR_som,area,1.0,1))

ff=(wgt_areaave_Wrap(TS_1CO2_sst,area,1.0,1))
; printVarSummary(a)
gg=(wgt_areaave_Wrap(TS_2CO2_sst,area,1.0,1))
hh=(wgt_areaave_Wrap(TS_UNIF_sst,area,1.0,1))
ii=(wgt_areaave_Wrap(TS_SOLAR_sst,area,1.0,1))

;asciiwrite ("./TS_1CO2_som.txt" ,  aa)
;asciiwrite ("./TS_2CO2_som.txt" ,  bb)
;asciiwrite ("./TS_UNIF_som.txt" ,  cc)
asciiwrite ("./TS_SOLAR_som_29.txt" ,  dd)
; asciiwrite ("./TS_TROP_som.txt" ,  e)

;asciiwrite ("./TS_1CO2_sst.txt" ,  ff)
;asciiwrite ("./TS_2CO2_sst.txt" ,  gg)
;asciiwrite ("./TS_UNIF_sst.txt" ,  hh)
asciiwrite ("./TS_SOLAR_sst_29.txt" ,  ii)

;asciiwrite ("./TS_TROP_sst.txt" ,  j)