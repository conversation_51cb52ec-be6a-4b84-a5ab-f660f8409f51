import matplotlib.pyplot as plt
import numpy as np

# Data for the graph - forcing types
forcing_types = ['1CO2', 'Solar', 'SAI']

# Temperature change values (in K) relative to 2CO2 - using actual data from HS_CSG_edit.R
# Negative values because we're doing 1CO2-2CO2, Solar-2CO2, SAI-2CO2
temp_change = {
    # SOM (coupled ocean) values
    'SOM': {
        '1CO2': -3.2804400,  # 1CO2 is cooler than 2CO2
        'Solar': -3.260,  # Solar is cooler than 2CO2
        'SAI': -3.2606300     # SAI is cooler than 2CO2
    },
    # SST (fixed SST) values
    'SST': {
        '1CO2': -0.2695800,  # Small temperature change in fixed SST
        'Solar': -0.159,  # Small temperature change in fixed SST
        'SAI': -0.1717367     # Small temperature change in fixed SST
    }
}

# Precipitation change values (in percentage) relative to 2CO2 - using actual data from HS_CSG_edit.R
precip_change = {
    # SOM (coupled ocean) values
    'SOM': {
        '1CO2': -5.9758618,  # 1CO2 has less precipitation than 2CO2
        'Solar': -7.967,  # Solar has less precipitation than 2CO2
        'SAI': -8.4302264     # SAI has less precipitation than 2CO2
    },
    # SST (fixed SST) values - fast response only
    'SST': {
        '1CO2': 2.4690197,   # 1CO2 has more precipitation than 2CO2 in fixed SST
        'Solar': 0.934,   # Solar has more precipitation than 2CO2 in fixed SST
        'SAI': 0.6194838      # SAI has more precipitation than 2CO2 in fixed SST
    }
}

# Calculate hydrological sensitivity (HS) as in the NCL script
# HS = (delta_PT_SOM - delta_PT_SST) / (delta_TS_SOM - delta_TS_SST)
HS = {}
for forcing in forcing_types:
    HS[forcing] = (precip_change['SOM'][forcing] - precip_change['SST'][forcing]) / \
                 (temp_change['SOM'][forcing] - temp_change['SST'][forcing])

# Calculate apparent hydrological sensitivity (AHS) as in the NCL script
# AHS = delta_PT_SOM / delta_TS_SOM
AHS = {}
for forcing in forcing_types:
    AHS[forcing] = precip_change['SOM'][forcing] / temp_change['SOM'][forcing]

print("Hydrological Sensitivity (HS):")
for forcing in forcing_types:
    print(f"{forcing}: {HS[forcing]:.2f} %/K")

print("\nApparent Hydrological Sensitivity (AHS):")
for forcing in forcing_types:
    print(f"{forcing}: {AHS[forcing]:.2f} %/K")

# Set up the figure and axis
fig, ax = plt.subplots(figsize=(10, 8))

# Colors for each forcing type
colors = {
    '1CO2': 'red',      # Red for CO2
    'Solar': 'green',    # Green for Solar
    'SAI': 'blue'       # Blue for SAI
}

# Plot precipitation vs temperature change
for forcing in forcing_types:
    # Plot SOM points (squares)
    ax.scatter(
        temp_change['SOM'][forcing],
        precip_change['SOM'][forcing],
        color=colors[forcing],
        marker='s',  # Square for SOM
        s=100,  # Size of marker
        label=f"{forcing} SOM"
    )

    # Plot SST points (triangles)
    ax.scatter(
        temp_change['SST'][forcing],
        precip_change['SST'][forcing],
        color=colors[forcing],
        marker='^',  # Triangle for SST
        s=100,  # Size of marker
        label=f"{forcing} SST"
    )

    # Draw lines connecting SOM and SST points
    ax.plot(
        [temp_change['SOM'][forcing], temp_change['SST'][forcing]],
        [precip_change['SOM'][forcing], precip_change['SST'][forcing]],
        color=colors[forcing],
        linestyle='-',
        linewidth=2
    )

# Add a horizontal line at y=0
ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)

# Add a vertical line at x=0
ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)

# Add labels and title
ax.set_xlabel('Δ Temperature (K)', fontsize=14)
ax.set_ylabel('Δ Precipitation (%)', fontsize=14)
ax.set_title('Generalized Hydrological Sensitivity', fontsize=16)

# Add grid
ax.grid(True, linestyle='--', alpha=0.7)

# Add a legend with custom handles
from matplotlib.lines import Line2D

# Create legend elements
legend_elements = []

# Control section (marker types)
legend_elements.append(Line2D([0], [0], marker='s', color='black', markerfacecolor='black',
                             markersize=8, linestyle='none', label='SOM'))
legend_elements.append(Line2D([0], [0], marker='^', color='black', markerfacecolor='black',
                             markersize=8, linestyle='none', label='SST'))

# Experiment section (colors)
legend_elements.append(Line2D([0], [0], marker='o', color='red', markerfacecolor='red',
                             markersize=8, linestyle='-', label='1CO2'))
legend_elements.append(Line2D([0], [0], marker='o', color='green', markerfacecolor='green',
                             markersize=8, linestyle='-', label='Solar'))
legend_elements.append(Line2D([0], [0], marker='o', color='blue', markerfacecolor='blue',
                             markersize=8, linestyle='-', label='SAI'))

# Create two-column legend
legend1 = ax.legend(handles=legend_elements[:2], loc='upper right', title='Control', fontsize=10, frameon=True)
legend2 = ax.legend(handles=legend_elements[2:], loc='lower right', title='Experiment', fontsize=10, frameon=True)

# Add the first legend back
ax.add_artist(legend1)

# Set axis limits to match the data
ax.set_xlim(-5.0, 0.5)
ax.set_ylim(-12, 4)

# Adjust layout and save the figure
plt.tight_layout()
plt.savefig('generalized_hydrological_sensitivity_plot_scaled.png', dpi=300)
plt.show()
