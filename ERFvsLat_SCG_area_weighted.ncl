load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/E2000_solin/Yearly_E2000_solin_01_100_1.nc", "r")

f_1CO2 = addfile(e_path+"/Yearly_F_CO2_01_60_new1.nc", "r")
f_2CO2 = addfile(e_path+"/Yearly_F_2CO2_01_60_new1.nc", "r")
f_UNIF_22_5 = addfile(e_path+"/Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc", "r")
f_SOLAR = addfile("/home/<USER>/Documents/Data_solin/F2000_solin/Yearly_F2000_solin_01_60_1.nc", "r")

; Calculate net radiation for SST runs
NET_RAD_SST_1CO2          = (/f_1CO2->FSNT(30:59,:,:) - f_1CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_2CO2          = (/f_2CO2->FSNT(30:59,:,:) - f_2CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_UNIF_22_5     = (/f_UNIF_22_5->FSNT(30:59,:,:) - f_UNIF_22_5->FLNT(30:59,:,:)/)
NET_RAD_SST_SOLAR         = (/f_SOLAR->FSNT(30:59,:,:) - f_SOLAR->FLNT(30:59,:,:)/)

; Calculate net radiation for SOM runs
NET_RAD_SOM_1CO2          = (/e_1CO2->FSNT(40:99,:,:) - e_1CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_2CO2          = (/e_2CO2->FSNT(40:99,:,:) - e_2CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_UNIF_22_5     = (/e_UNIF_22_5->FSNT(40:99,:,:) - e_UNIF_22_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_SOLAR         = (/e_SOLAR->FSNT(40:99,:,:) - e_SOLAR->FLNT(40:99,:,:)/)

; Copy coordinates
dummy1=f_1CO2->TS(:,:,:)
dummy2=e_1CO2->TS(:,:,:)

copy_VarCoords(dummy1,NET_RAD_SST_1CO2)
copy_VarCoords(dummy1,NET_RAD_SST_2CO2)
copy_VarCoords(dummy1,NET_RAD_SST_UNIF_22_5)
copy_VarCoords(dummy1,NET_RAD_SST_SOLAR)

copy_VarCoords(dummy2,NET_RAD_SOM_1CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_2CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_UNIF_22_5)
copy_VarCoords(dummy2,NET_RAD_SOM_SOLAR)

; Calculate area weights
lat=f_1CO2->lat
lon=f_1CO2->lon

; Create weights for area-weighted averaging
rad    = 4.0*atan(1.0)/180.0
clat   = cos(lat*rad)  ; cosine of latitude for area weighting



; Calculate zonal means with proper area weighting
; For 2CO2-1CO2
; First average over time dimension (dim 0)
NET_RAD_SST_2CO2_time_avg = dim_avg_n_Wrap(NET_RAD_SST_2CO2, 0)
NET_RAD_SST_1CO2_time_avg = dim_avg_n_Wrap(NET_RAD_SST_1CO2, 0)
; Then do simple average over longitude (dim 1) for each latitude
delta_N_SST_2CO2_lat = dim_avg_n_Wrap(NET_RAD_SST_2CO2_time_avg, 1) - dim_avg_n_Wrap(NET_RAD_SST_1CO2_time_avg, 1)

; For UNIF-2CO2
NET_RAD_SST_UNIF_22_5_time_avg = dim_avg_n_Wrap(NET_RAD_SST_UNIF_22_5, 0)
delta_N_SST_UNIF_22_5_lat = dim_avg_n_Wrap(NET_RAD_SST_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(NET_RAD_SST_2CO2_time_avg, 1)

; For SOLAR-2CO2
NET_RAD_SST_SOLAR_time_avg = dim_avg_n_Wrap(NET_RAD_SST_SOLAR, 0)
delta_N_SST_SOLAR_lat = dim_avg_n_Wrap(NET_RAD_SST_SOLAR_time_avg, 1) - dim_avg_n_Wrap(NET_RAD_SST_2CO2_time_avg, 1)

; For UNIF-1CO2
delta_N_SST_UNIF_1CO2_lat = dim_avg_n_Wrap(NET_RAD_SST_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(NET_RAD_SST_1CO2_time_avg, 1)

; For SOLAR-1CO2
delta_N_SST_SOLAR_1CO2_lat = dim_avg_n_Wrap(NET_RAD_SST_SOLAR_time_avg, 1) - dim_avg_n_Wrap(NET_RAD_SST_1CO2_time_avg, 1)

; Calculate temperature differences with area weighting
; For 2CO2-1CO2
TS_SST_2CO2_time_avg = dim_avg_n_Wrap(f_2CO2->TS(30:59,:,:), 0)
TS_SST_1CO2_time_avg = dim_avg_n_Wrap(f_1CO2->TS(30:59,:,:), 0)
DELTA_T_SST_2CO2_lat = dim_avg_n_Wrap(TS_SST_2CO2_time_avg, 1) - dim_avg_n_Wrap(TS_SST_1CO2_time_avg, 1)

; For UNIF-2CO2
TS_SST_UNIF_22_5_time_avg = dim_avg_n_Wrap(f_UNIF_22_5->TS(30:59,:,:), 0)
DELTA_T_SST_UNIF_22_5_lat = dim_avg_n_Wrap(TS_SST_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(TS_SST_2CO2_time_avg, 1)

; For SOLAR-2CO2
TS_SST_SOLAR_time_avg = dim_avg_n_Wrap(f_SOLAR->TS(30:59,:,:), 0)
DELTA_T_SST_SOLAR_lat = dim_avg_n_Wrap(TS_SST_SOLAR_time_avg, 1) - dim_avg_n_Wrap(TS_SST_2CO2_time_avg, 1)

; For UNIF-1CO2
DELTA_T_SST_UNIF_1CO2_lat = dim_avg_n_Wrap(TS_SST_UNIF_22_5_time_avg, 1) - dim_avg_n_Wrap(TS_SST_1CO2_time_avg, 1)

; For SOLAR-1CO2
DELTA_T_SST_SOLAR_1CO2_lat = dim_avg_n_Wrap(TS_SST_SOLAR_time_avg, 1) - dim_avg_n_Wrap(TS_SST_1CO2_time_avg, 1)

; Calculate global mean differences for SST runs with proper area weighting
; For 2CO2-1CO2
DELTA_N_SST_2CO2_GLOBALmean = wgt_areaave_Wrap(NET_RAD_SST_2CO2,clat, 1.0, 0) - wgt_areaave_Wrap(NET_RAD_SST_1CO2,clat, 1.0, 0)
DELTA_T_SST_2CO2_GLOBALmean = wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),clat, 1.0, 0)

; For UNIF-2CO2
DELTA_N_SST_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5,clat, 1.0, 0) - wgt_areaave_Wrap(NET_RAD_SST_2CO2,clat, 1.0, 0)
DELTA_T_SST_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),clat, 1.0, 0)

; For SOLAR-2CO2
DELTA_N_SST_SOLAR_GLOBALmean = wgt_areaave_Wrap(NET_RAD_SST_SOLAR,clat, 1.0, 0) - wgt_areaave_Wrap(NET_RAD_SST_2CO2,clat, 1.0, 0)
DELTA_T_SST_SOLAR_GLOBALmean = wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),clat, 1.0, 0)

; For UNIF-1CO2
DELTA_N_SST_UNIF_1CO2_GLOBALmean = wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5,clat, 1.0, 0) - wgt_areaave_Wrap(NET_RAD_SST_1CO2,clat, 1.0, 0)
DELTA_T_SST_UNIF_1CO2_GLOBALmean = wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),clat, 1.0, 0)

; For SOLAR-1CO2
DELTA_N_SST_SOLAR_1CO2_GLOBALmean = wgt_areaave_Wrap(NET_RAD_SST_SOLAR,clat, 1.0, 0) - wgt_areaave_Wrap(NET_RAD_SST_1CO2,clat, 1.0, 0)
DELTA_T_SST_SOLAR_1CO2_GLOBALmean = wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),clat, 1.0, 0)

; Calculate global mean differences for SOM runs with proper area weighting
; For 2CO2-1CO2
DELTA_N_SOM_2CO2_GLOBALmean = wgt_areaave_Wrap(NET_RAD_SOM_2CO2,clat, 1.0, 0) - wgt_areaave_Wrap(NET_RAD_SOM_1CO2,clat, 1.0, 0)
DELTA_T_SOM_2CO2_GLOBALmean = wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),clat, 1.0, 0)

; For UNIF-2CO2
DELTA_N_SOM_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5,clat, 1.0, 0) - wgt_areaave_Wrap(NET_RAD_SOM_2CO2,clat, 1.0, 0)
DELTA_T_SOM_UNIF_GLOBALmean_22_5 = wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),clat, 1.0, 0)

; For SOLAR-2CO2
DELTA_N_SOM_SOLAR_GLOBALmean = wgt_areaave_Wrap(NET_RAD_SOM_SOLAR,clat, 1.0, 0) - wgt_areaave_Wrap(NET_RAD_SOM_2CO2,clat, 1.0, 0)
DELTA_T_SOM_SOLAR_GLOBALmean = wgt_areaave_Wrap(e_SOLAR->TS(40:99,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),clat, 1.0, 0)

; For UNIF-1CO2
DELTA_N_SOM_UNIF_1CO2_GLOBALmean = wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5,clat, 1.0, 0) - wgt_areaave_Wrap(NET_RAD_SOM_1CO2,clat, 1.0, 0)
DELTA_T_SOM_UNIF_1CO2_GLOBALmean = wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),clat, 1.0, 0)

; For SOLAR-1CO2
DELTA_N_SOM_SOLAR_1CO2_GLOBALmean = wgt_areaave_Wrap(NET_RAD_SOM_SOLAR,clat, 1.0, 0) - wgt_areaave_Wrap(NET_RAD_SOM_1CO2,clat, 1.0, 0)
DELTA_T_SOM_SOLAR_1CO2_GLOBALmean = wgt_areaave_Wrap(e_SOLAR->TS(40:99,:,:),clat, 1.0, 0) - wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),clat, 1.0, 0)

; Calculate lambda for each forcing type
; For 2CO2-1CO2
Lamda_2CO2 = (dim_avg_n_Wrap(DELTA_N_SST_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_2CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_2CO2_GLOBALmean, 0))

; For UNIF-2CO2
Lamda_UNIF_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0))

; For SOLAR-2CO2
Lamda_SOLAR = (dim_avg_n_Wrap(DELTA_N_SST_SOLAR_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_SOLAR_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_SOLAR_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_SOLAR_GLOBALmean, 0))

; For UNIF-1CO2
Lamda_UNIF_1CO2 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_1CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_UNIF_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_UNIF_1CO2_GLOBALmean, 0))

; For SOLAR-1CO2
Lamda_SOLAR_1CO2 = (dim_avg_n_Wrap(DELTA_N_SST_SOLAR_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_SOLAR_1CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_SOLAR_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_SOLAR_1CO2_GLOBALmean, 0))

; Print lambda values
print("Lambda values:")
print("Lambda_2CO2 = " + Lamda_2CO2)
print("Lambda_UNIF_22_5 = " + Lamda_UNIF_22_5)
print("Lambda_SOLAR = " + Lamda_SOLAR)
print("Lambda_UNIF_1CO2 = " + Lamda_UNIF_1CO2)
print("Lambda_SOLAR_1CO2 = " + Lamda_SOLAR_1CO2)

; Calculate ERF for each latitude
; Create a new array to hold all five ERF zonal profiles
ERF = new((/5, dimsizes(lat)/), float)

; For 2CO2-1CO2
ERF(0,:) = delta_N_SST_2CO2_lat - Lamda_2CO2 * DELTA_T_SST_2CO2_lat

; For UNIF-2CO2
ERF(1,:) = delta_N_SST_UNIF_22_5_lat - Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5_lat

; For SOLAR-2CO2
ERF(2,:) = delta_N_SST_SOLAR_lat - Lamda_SOLAR * DELTA_T_SST_SOLAR_lat

; For UNIF-1CO2
ERF(3,:) = delta_N_SST_UNIF_1CO2_lat - Lamda_UNIF_1CO2 * DELTA_T_SST_UNIF_1CO2_lat

; For SOLAR-1CO2
ERF(4,:) = delta_N_SST_SOLAR_1CO2_lat - Lamda_SOLAR_1CO2 * DELTA_T_SST_SOLAR_1CO2_lat

; Calculate global mean ERF values
deg2rad = 4.0 * atan(1.0) / 180.0
coslat  = cos(lat*rad)

; For ERF_2CO2
ERF_2CO2 = dim_sum_n(ERF(0,:) * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_UNIF_22_5
ERF_UNIF_22_5 = dim_sum_n(ERF(1,:) * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_SOLAR
ERF_SOLAR = dim_sum_n(ERF(2,:) * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_UNIF_1CO2
ERF_UNIF_1CO2 = dim_sum_n(ERF(3,:) * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_SOLAR_1CO2
ERF_SOLAR_1CO2 = dim_sum_n(ERF(4,:) * coslat, 0) / dim_sum_n(coslat, 0)

; Print the global mean ERF values
print("Global mean ERF values:")
print("2CO2 (relative to 1CO2): " + ERF_2CO2)
print("UNIF (relative to 2CO2): " + ERF_UNIF_22_5)
print("SOLAR (relative to 2CO2): " + ERF_SOLAR)
print("UNIF (relative to 1CO2): " + ERF_UNIF_1CO2)
print("SOLAR (relative to 1CO2): " + ERF_SOLAR_1CO2)
print(ERF)
print(lat)
;plotting
;***********************************************************
; Plot: Radiative Forcing vs Latitude
;***********************************************************

;wks = gsn_open_wks("pdf","ERF_vs_lat_SCG_area_weighted")   ; or "png" instead of "pdf"
wks = gsn_open_wks("png","ERF_vs_lat_SCG_area_weighted")   ; or "png" instead of "pdf"

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"red","blue","green","blue","green"/)
res@xyLineThicknesses = (/2.5, 2.5, 2.5, 2.5, 2.5/)
res@xyDashPatterns    = (/0, 0, 0, 2, 2/)         ; solid for first 3, dotted for last 2
res@gsnXYTopLabel     = False
res@tiMainString      = "Effective Radiative Forcing vs Latitude (Area-Weighted)"
res@tiYAxisString     = "ERF (Wm~S~-2~NN~)"
res@tiXAxisString     = "Latitude (~F34~0~F~)"
res@trXMinF           = -90
res@trXMaxF           = 90

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"2xCO~B~2", "SAI-2CO2", "SOLAR-2CO2", "SAI-1CO2", "SOLAR-1CO2"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.25                       ;-- define legend width
res@pmLegendHeightF        =  0.20
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

plot = gsn_csm_xy(wks, lat, ERF, res)

exit()

end



