load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************



; e_path="/Volumes/Backup Plus"
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")

e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/E2000_solin/Yearly_E2000_solin_01_100_1.nc", "r")





f_1CO2 = addfile(e_path+"/Yearly_F_CO2_01_60_new1.nc", "r")
f_2CO2 = addfile(e_path+"/Yearly_F_2CO2_01_60_new1.nc", "r")

f_UNIF_22_5 = addfile(e_path+"/Yearly_F2000_2CO2_UNIF_01_60_2D_1.nc", "r")
f_SOLAR = addfile("/home/<USER>/Documents/Data_solin/F2000_solin/Yearly_F2000_solin_01_60_1.nc", "r")



NET_RAD_SST_1CO2          = (/f_1CO2->FSNT(30:59,:,:) - f_1CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_2CO2          = (/f_2CO2->FSNT(30:59,:,:) - f_2CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_UNIF_22_5          = (/f_UNIF_22_5->FSNT(30:59,:,:) - f_UNIF_22_5->FLNT(30:59,:,:)/)
NET_RAD_SST_SOLAR          = (/f_SOLAR->FSNT(30:59,:,:) - f_SOLAR->FLNT(30:59,:,:)/)


NET_RAD_SOM_1CO2          = (/e_1CO2->FSNT(40:99,:,:) - e_1CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_2CO2          = (/e_2CO2->FSNT(40:99,:,:) - e_2CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_UNIF_22_5          = (/e_UNIF_22_5->FSNT(40:99,:,:) - e_UNIF_22_5->FLNT(40:99,:,:)/)
NET_RAD_SOM_SOLAR          = (/e_SOLAR->FSNT(40:99,:,:) - e_SOLAR->FLNT(40:99,:,:)/)

;---------------------------------------------------------
dummy1=f_1CO2->TS(:,:,:)
dummy2=e_1CO2->TS(:,:,:)

copy_VarCoords(dummy1,NET_RAD_SST_1CO2)
copy_VarCoords(dummy1,NET_RAD_SST_2CO2)
copy_VarCoords(dummy1,NET_RAD_SST_UNIF_22_5)
copy_VarCoords(dummy1,NET_RAD_SST_SOLAR)



copy_VarCoords(dummy2,NET_RAD_SOM_1CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_2CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_UNIF_22_5)
;copy_VarCoords(dummy2,NET_RAD_SOM_SOLAR)

;******************************************                 Areal average
lat=f_1CO2->lat
lon=f_1CO2->lon

  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************
dummy3=f_1CO2->lat
copy_VarCoords(dummy3,area)
print(area)


; Calculate differences with respect to 1CO2 instead of 2CO2
DELTA_N_SST_2CO2 = (wgt_areaave_Wrap(NET_RAD_SST_2CO2(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2(:,:,:),area(:),1.0,1))  ; 2CO2 - 1CO2
DELTA_N_SST_UNIF_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2(:,:,:),area(:),1.0,1))  ; UNIF - 1CO2
DELTA_N_SST_SOLAR = (wgt_areaave_Wrap(NET_RAD_SST_SOLAR(:,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2(:,:,:),area(:),1.0,1))  ; SOLAR - 1CO2

; Calculate temperature differences with respect to 1CO2 instead of 2CO2
DELTA_T_SST_2CO2 = (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area(:),1.0,1))  ; 2CO2 - 1CO2
DELTA_T_SST_UNIF_22_5 = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area(:),1.0,1))  ; UNIF - 1CO2
DELTA_T_SST_SOLAR = (wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:),area(:),1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area(:),1.0,1))  ; SOLAR - 1CO2

; Calculate global mean differences with respect to 1CO2 instead of 2CO2
; Net radiation differences for SST runs
DELTA_N_SST_2CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_2CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1))  ; 2CO2 - 1CO2
DELTA_N_SST_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1))  ; UNIF - 1CO2
DELTA_N_SST_SOLAR_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SST_SOLAR,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1))  ; SOLAR - 1CO2

; Temperature differences for SST runs
DELTA_T_SST_2CO2_GLOBALmean = (wgt_areaave_Wrap(f_2CO2->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1))  ; 2CO2 - 1CO2
DELTA_T_SST_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_UNIF_22_5->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1))  ; UNIF - 1CO2
DELTA_T_SST_SOLAR_GLOBALmean_22_5 = (wgt_areaave_Wrap(f_SOLAR->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1))  ; SOLAR - 1CO2

; Net radiation differences for SOM runs
DELTA_N_SOM_2CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_2CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1))  ; 2CO2 - 1CO2
DELTA_N_SOM_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_UNIF_22_5,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1))  ; UNIF - 1CO2
DELTA_N_SOM_SOLAR_GLOBALmean_22_5 = (wgt_areaave_Wrap(NET_RAD_SOM_SOLAR,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1))  ; SOLAR - 1CO2

; Temperature differences for SOM runs
DELTA_T_SOM_2CO2_GLOBALmean = (wgt_areaave_Wrap(e_2CO2->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1))  ; 2CO2 - 1CO2
DELTA_T_SOM_UNIF_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_UNIF_22_5->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1))  ; UNIF - 1CO2
DELTA_T_SOM_SOLAR_GLOBALmean_22_5 = (wgt_areaave_Wrap(e_SOLAR->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1))  ; SOLAR - 1CO2

; Print variable dimensions to debug the dimension mismatch
print("DELTA_T_SOM_2CO2_GLOBALmean dimensions: ")
printVarSummary(DELTA_T_SOM_2CO2_GLOBALmean)
print("DELTA_T_SST_2CO2_GLOBALmean dimensions: ")
printVarSummary(DELTA_T_SST_2CO2_GLOBALmean)
print("DELTA_T_SOM_UNIF_GLOBALmean_22_5 dimensions: ")
printVarSummary(DELTA_T_SOM_UNIF_GLOBALmean_22_5)
print("DELTA_T_SST_UNIF_GLOBALmean_22_5 dimensions: ")
printVarSummary(DELTA_T_SST_UNIF_GLOBALmean_22_5)

; Calculate slow response (SOM-SST) for surface temperature change
; Make sure we're working with compatible dimensions by using dim_avg_n_Wrap
DELTA_T_SLOW_2CO2 = dim_avg_n_Wrap(DELTA_T_SOM_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_2CO2_GLOBALmean, 0)  ; 2CO2 - 1CO2
DELTA_T_SLOW_UNIF_22_5 = dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0)  ; UNIF - 1CO2
DELTA_T_SLOW_SOLAR = dim_avg_n_Wrap(DELTA_T_SOM_SOLAR_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SST_SOLAR_GLOBALmean_22_5, 0)  ; SOLAR - 1CO2

; Lamda_2CO2 = (dim_avg_n_Wrap(DELTA_N_SST_2CO2_GLOBALmean(30:59), 0) - dim_avg_n_Wrap(DELTA_N_SOM_2CO2_GLOBALmean(40:99), 0)) / (dim_avg_n_Wrap(DELTA_T_SST_2CO2_GLOBALmean(30:59), 0) - dim_avg_n_Wrap(DELTA_T_SOM_2CO2_GLOBALmean(40:99), 0))
; ERF_2CO2 = dim_avg_n_Wrap((DELTA_N_SST_2CO2(30:59) - (Lamda_2CO2 * DELTA_T_SST_2CO2(30:59))),0)
printVarSummary(DELTA_T_SST_2CO2_GLOBALmean)
; Calculate lambda values using the formula: λ = (ΔNSST – ΔNSOM) / (ΔTSOM – ΔTSST)
; Note: The denominator is (ΔTSOM – ΔTSST) which is different from the previous formula
Lamda_2CO2 = (dim_avg_n_Wrap(DELTA_N_SST_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_2CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_2CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SST_2CO2_GLOBALmean, 0))
Lamda_UNIF_22_5 = (dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0))
Lamda_SOLAR = (dim_avg_n_Wrap(DELTA_N_SST_SOLAR_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_N_SOM_SOLAR_GLOBALmean_22_5, 0)) / (dim_avg_n_Wrap(DELTA_T_SOM_SOLAR_GLOBALmean_22_5, 0) - dim_avg_n_Wrap(DELTA_T_SST_SOLAR_GLOBALmean_22_5, 0))


; Calculate radiative forcing using the fast response (SST runs): F = ΔNSST + λ ΔTSST
; Note: This is using + instead of - compared to the previous formula
ERF_2CO2 = dim_avg_n_Wrap((DELTA_N_SST_2CO2 + (Lamda_2CO2 * DELTA_T_SST_2CO2)),0)
ERF_UNIF_22_5 = dim_avg_n_Wrap((DELTA_N_SST_UNIF_22_5 + (Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5)),0)
ERF_SOLAR = dim_avg_n_Wrap((DELTA_N_SST_SOLAR + (Lamda_SOLAR * DELTA_T_SST_SOLAR)),0)

; Create array for ERF values
ERF=new((/32/),typeof(dummy2),dummy2@_FillValue)
ERF(0) = dim_avg_n_Wrap((DELTA_N_SST_2CO2 + (Lamda_2CO2 * DELTA_T_SST_2CO2)),0)  ; 2CO2 - 1CO2
ERF(1) = dim_avg_n_Wrap((DELTA_N_SST_UNIF_22_5 + (Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5)),0)  ; UNIF - 1CO2
ERF(2) = dim_avg_n_Wrap((DELTA_N_SST_SOLAR + (Lamda_SOLAR * DELTA_T_SST_SOLAR)),0)  ; SOLAR - 1CO2

; We only keep the main radiative forcing calculation: F = ΔNSST + λ ΔTSST

; Calculate RF by latitude for plotting
; First create temporary variables with proper _FillValue attributes
temp_SOM_1CO2 = e_1CO2->TS(40:99,:,:)
temp_SOM_2CO2 = e_2CO2->TS(40:99,:,:)
temp_SST_1CO2 = f_1CO2->TS(30:59,:,:)
temp_SST_2CO2 = f_2CO2->TS(30:59,:,:)
temp_SOM_UNIF = e_UNIF_22_5->TS(40:99,:,:)
temp_SST_UNIF = f_UNIF_22_5->TS(30:59,:,:)
temp_SOM_SOLAR = e_SOLAR->TS(40:99,:,:)
temp_SST_SOLAR = f_SOLAR->TS(30:59,:,:)

; Calculate differences with respect to 1CO2
temp_diff_SOM_2CO2 = temp_SOM_2CO2 - temp_SOM_1CO2  ; 2CO2 - 1CO2
temp_diff_SST_2CO2 = temp_SST_2CO2 - temp_SST_1CO2  ; 2CO2 - 1CO2
temp_diff_SOM_UNIF = temp_SOM_UNIF - temp_SOM_2CO2  ; UNIF - 1CO2
temp_diff_SST_UNIF = temp_SST_UNIF - temp_SST_2CO2  ; UNIF - 1CO2
temp_diff_SOM_SOLAR = temp_SOM_SOLAR - temp_SOM_2CO2  ; SOLAR - 1CO2
temp_diff_SST_SOLAR = temp_SST_SOLAR - temp_SST_2CO2  ; SOLAR - 1CO2

; Calculate zonal means (average over time and longitude)
DELTA_T_SOM_2CO2_zonal = dim_avg_n(dim_avg_n(temp_diff_SOM_2CO2, 0), 1)  ; average over time and longitude, 2CO2 - 1CO2
DELTA_T_SST_2CO2_zonal = dim_avg_n(dim_avg_n(temp_diff_SST_2CO2, 0), 1)  ; average over time and longitude, 2CO2 - 1CO2
DELTA_T_SOM_UNIF_22_5_zonal = dim_avg_n(dim_avg_n(temp_diff_SOM_UNIF, 0), 1)  ; average over time and longitude, UNIF - 1CO2
DELTA_T_SST_UNIF_22_5_zonal = dim_avg_n(dim_avg_n(temp_diff_SST_UNIF, 0), 1)  ; average over time and longitude, UNIF - 1CO2
DELTA_T_SOM_SOLAR_zonal = dim_avg_n(dim_avg_n(temp_diff_SOM_SOLAR, 0), 1)  ; average over time and longitude, SOLAR - 1CO2
DELTA_T_SST_SOLAR_zonal = dim_avg_n(dim_avg_n(temp_diff_SST_SOLAR, 0), 1)  ; average over time and longitude, SOLAR - 1CO2

; Then calculate slow response (SOM-SST) by latitude
DELTA_T_SLOW_2CO2_zonal = DELTA_T_SOM_2CO2_zonal - DELTA_T_SST_2CO2_zonal  ; 2CO2 - 1CO2
DELTA_T_SLOW_UNIF_22_5_zonal = DELTA_T_SOM_UNIF_22_5_zonal - DELTA_T_SST_UNIF_22_5_zonal  ; UNIF - 1CO2
DELTA_T_SLOW_SOLAR_zonal = DELTA_T_SOM_SOLAR_zonal - DELTA_T_SST_SOLAR_zonal  ; SOLAR - 1CO2


; Calculate RF by latitude using the formula F = ΔNSST + λ ΔTSST
; First calculate zonal means of NSST
DELTA_N_SST_2CO2_zonal = dim_avg_n(dim_avg_n(NET_RAD_SST_2CO2 - NET_RAD_SST_1CO2, 0), 1)  ; average over time and longitude
DELTA_N_SST_UNIF_22_5_zonal = dim_avg_n(dim_avg_n(NET_RAD_SST_UNIF_22_5 - NET_RAD_SST_2CO2, 0), 1)  ; average over time and longitude
DELTA_N_SST_SOLAR_zonal=dim_avg_n(dim_avg_n(NET_RAD_SST_SOLAR - NET_RAD_SST_2CO2, 0), 1)

; Calculate RF by latitude
RF_2CO2_zonal = DELTA_N_SST_2CO2_zonal + (Lamda_2CO2 * DELTA_T_SST_2CO2_zonal)  ; 2CO2 - 1CO2
RF_UNIF_22_5_zonal = DELTA_N_SST_UNIF_22_5_zonal + (Lamda_UNIF_22_5 * DELTA_T_SST_UNIF_22_5_zonal)  ; UNIF - 1CO2
RF_SOLAR_zonal = DELTA_N_SST_SOLAR_zonal + (Lamda_SOLAR * DELTA_T_SST_SOLAR_zonal)  ; UNIF - 1CO2

; Copy coordinate metadata
copy_VarCoords(lat, RF_2CO2_zonal)
copy_VarCoords(lat, RF_UNIF_22_5_zonal)
copy_VarCoords(lat, RF_SOLAR_zonal)
copy_VarCoords(lat, DELTA_N_SST_2CO2_zonal)
copy_VarCoords(lat, DELTA_N_SST_UNIF_22_5_zonal)
copy_VarCoords(lat, DELTA_N_SST_SOLAR_zonal)
copy_VarCoords(lat, DELTA_T_SST_2CO2_zonal)
copy_VarCoords(lat, DELTA_T_SST_UNIF_22_5_zonal)
copy_VarCoords(lat, DELTA_T_SST_SOLAR_zonal)


; ERF_2CO2_1 = dim_avg_n_Wrap((DELTA_N_SST_2CO2(30:59) - (Lamda_2CO2 * DELTA_T_SST_2CO2(30:59))),0)

;  print(Label_Global_mean)
;------------------------------------------------
; asciiwrite("Change_AOD_mean_rectangular_grid.txt",Label_Global_mean)
; asciiwrite("Change_AOD_mean_rectangular_grid_2point.txt",Label_Global_mean1)
; asciiwrite("Change_AOD_mean_rectangular_grid_all.txt",Global_mean)
; print(Lamda_UNIF_22_5)
print("Radiative Forcing calculation (F = ΔNSST + λ ΔTSST):")
print(ERF)
print("")
print("Lambda values:")
print("Lambda_2CO2 = " + Lamda_2CO2)
print("Lambda_UNIF_22_5 = " + Lamda_UNIF_22_5)
print("")
print("Slow response temperature changes (SOM-SST):")
print("DELTA_T_SLOW_2CO2 = " + dim_avg_n_Wrap(DELTA_T_SLOW_2CO2, 0))
print("DELTA_T_SLOW_UNIF_22_5 = " + dim_avg_n_Wrap(DELTA_T_SLOW_UNIF_22_5, 0))
print("")
print("Original data for reference:")
print("DELTA_T_SST_2CO2 = ")
print(DELTA_T_SST_2CO2)
print("DELTA_N_SOM_UNIF_GLOBALmean_22_5 = ")
print((dim_avg_n_Wrap(DELTA_N_SOM_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SOM_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_N_SST_UNIF_GLOBALmean_22_5, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_15, 0)))
; print((dim_avg_n_Wrap(DELTA_T_SST_UNIF_GLOBALmean_22_5, 0)))

; Save original ERF results
; asciiwrite("ERF_globalmean_rectangular_grid_all.txt",ERF)
;asciiwrite("ERF_globalmean_rectangular_grid_all_new_2lev.txt",ERF)

; Save radiative forcing results
;asciiwrite("RF_globalmean_wrt_1CO2.txt",ERF)

;----------------------------------------------------------------------
; Create plot of RF vs latitude
;----------------------------------------------------------------------

; Set up the output workstation
;wks = gsn_open_wks("png","RF_vs_latitude_wrt_1CO2")
wks = gsn_open_wks("png","RF_vs_latitude_wrt_generic")

; Set up resources for the plot
res                   = True
res@gsnDraw           = False             ; Don't draw plot yet
res@gsnFrame          = False             ; Don't advance frame yet
res@gsnMaximize       = True              ; Maximize plot in frame

; Add a legend
res@pmLegendDisplayMode    = "Always"            ; Turn on legend
res@pmLegendSide           = "Top"               ; Change location of
res@pmLegendParallelPosF   = 0.85                ; Move legend to right
res@pmLegendOrthogonalPosF = -0.35               ; Move legend down
res@pmLegendWidthF         = 0.15                ; Change width and
res@pmLegendHeightF        = 0.15                ; height of legend
res@lgLabelFontHeightF     = 0.015
res@lgPerimOn              = False               ; Turn off legend perimeter

; X-axis labels
res@tiXAxisString        = "Latitude"           ; X-axis label
res@tiXAxisFontHeightF   = 0.02                 ; X-axis font size

; Y-axis labels
res@tiYAxisString        = "Radiative Forcing (W/m~S~2~N~)"  ; Y-axis label
res@tiYAxisFontHeightF   = 0.02                 ; Y-axis font size

; Main title
res@tiMainString         = "Radiative Forcing vs Latitude (wrt 1CO2, F = ΔNSST + λ ΔTSST)"  ; Main title
res@tiMainFontHeightF    = 0.025                ; Main title font size

; Line styles and colors
res@xyLineThicknessF     = 3.0                  ; Line thickness
res@xyLineColors         = (/"red", "blue","green"/)     ; Line colors
res@xyDashPatterns       = (/0, 0, 0/)             ; Line dash patterns (0=solid)
res@xyExplicitLegendLabels = (/"Global warming", "SAI","SOLAR"/) ; Legend labels

; Print dimensions to debug the plotting error
print("lat dimensions: ")
printVarSummary(lat)
print("RF_2CO2_zonal dimensions: ")
printVarSummary(RF_2CO2_zonal)
print("RF_UNIF_22_5_zonal dimensions: ")
printVarSummary(RF_UNIF_22_5_zonal)

; Create a multi-dimensional array for plotting
plotData = new((/3, dimsizes(lat)/), float)
plotData(0,:) = RF_2CO2_zonal  ; 2CO2 - 1CO2
plotData(1,:) = RF_UNIF_22_5_zonal  ; UNIF - 1CO2
plotData(2,:) = RF_SOLAR_zonal  ; SOLAR - 1CO2

; Create the plot
plot = gsn_csm_xy(wks, lat, plotData, res)

; Draw the plot
draw(plot)
frame(wks)

;----------------------------------------------------------------------
; Create a second plot of temperature difference vs latitude
;----------------------------------------------------------------------

; Set up the output workstation for temperature plot
wks_temp = gsn_open_wks("png","Temperature_Difference_vs_latitude_wrt_1CO2_generic")

; Modify resources for the temperature plot
res@tiMainString         = "Temperature Difference vs Latitude (wrt 1CO2)"  ; Main title
res@tiYAxisString        = "Temperature Difference (K)"             ; Y-axis label

; Print dimensions to debug the plotting error
print("DELTA_T_SST_2CO2_zonal dimensions: ")
printVarSummary(DELTA_T_SST_2CO2_zonal)
print("DELTA_T_SST_UNIF_22_5_zonal dimensions: ")
printVarSummary(DELTA_T_SST_UNIF_22_5_zonal)

; Create a multi-dimensional array for temperature plotting
tempPlotData = new((/3, dimsizes(lat)/), float)
tempPlotData(0,:) = DELTA_T_SST_2CO2_zonal  ; 2CO2 - 1CO2
tempPlotData(1,:) = DELTA_T_SST_UNIF_22_5_zonal  ; UNIF - 1CO2
tempPlotData(2,:) = DELTA_T_SST_SOLAR_zonal  ; SOLAR - 1CO2

; Create the temperature plot
temp_plot = gsn_csm_xy(wks_temp, lat, tempPlotData, res)

; Draw the temperature plot
draw(temp_plot)
frame(wks_temp)

; Save the zonal RF data to a file
zonal_rf_data = new((/3, dimsizes(lat)/), float)
zonal_rf_data(0,:) = RF_2CO2_zonal  ; 2CO2 - 1CO2
zonal_rf_data(1,:) = RF_UNIF_22_5_zonal  ; UNIF - 1CO2
zonal_rf_data(2,:) = RF_SOLAR_zonal  ; SOLAR - 1CO2
;asciiwrite("RF_zonal_wrt_1CO2.txt", zonal_rf_data)

; Save the zonal temperature difference data to a file
zonal_temp_data = new((/3, dimsizes(lat)/), float)
zonal_temp_data(0,:) = DELTA_T_SST_2CO2_zonal  ; 2CO2 - 1CO2
zonal_temp_data(1,:) = DELTA_T_SST_UNIF_22_5_zonal  ; UNIF - 1CO2
zonal_temp_data(2,:) = DELTA_T_SST_SOLAR_zonal  ; SOLAR - 1CO2
;asciiwrite("Temperature_Difference_zonal_wrt_1CO2.txt", zonal_temp_data)

  exit()

end



